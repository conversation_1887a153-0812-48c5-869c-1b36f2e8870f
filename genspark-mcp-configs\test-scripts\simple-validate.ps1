Write-Host "验证标准 MCP 配置..." -ForegroundColor Cyan
Write-Host ""

# 检查配置文件
Write-Host "检查配置文件..." -ForegroundColor Green

if (Test-Path "..\.mcp.json") {
    Write-Host "找到 .mcp.json 配置文件" -ForegroundColor Green
    $config = Get-Content "..\.mcp.json" | ConvertFrom-Json
    Write-Host "服务器数量: $($config.mcpServers.PSObject.Properties.Count)" -ForegroundColor Gray
} else {
    Write-Host "未找到 .mcp.json 配置文件" -ForegroundColor Yellow
}

if (Test-Path "..\simple-mcp-server-standard.json") {
    Write-Host "找到 simple-mcp-server-standard.json 配置文件" -ForegroundColor Green
} else {
    Write-Host "未找到 simple-mcp-server-standard.json 配置文件" -ForegroundColor Yellow
}

Write-Host ""

# 测试服务器连接
Write-Host "测试 Simple MCP Server 连接..." -ForegroundColor Green

$headers = @{'Content-Type' = 'application/json'}
$body = '{"jsonrpc": "2.0", "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "Test", "version": "1.0.0"}}, "id": 1}'

try {
    $response = Invoke-WebRequest -Uri "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp" -Method POST -Headers $headers -Body $body
    Write-Host "Simple MCP Server 连接成功" -ForegroundColor Green
} catch {
    Write-Host "Simple MCP Server 连接失败" -ForegroundColor Red
}

Write-Host ""
Write-Host "推荐的标准配置文件内容:" -ForegroundColor Yellow
Write-Host '{
  "mcpServers": {
    "simple-mcp-server": {
      "type": "http",
      "url": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}' -ForegroundColor Gray

Write-Host ""
Write-Host "配置完成!" -ForegroundColor Cyan

@echo off
chcp 65001 >nul
echo.
echo ========================================
echo      内网穿透设置向导
echo ========================================
echo.

echo 📋 步骤说明:
echo 1. 确保 MCP HTTP Bridge 服务器正在运行 (端口 3000)
echo 2. 选择内网穿透工具
echo 3. 获取公网访问 URL
echo 4. 配置外部服务 (Coze/GenSpark)
echo.

:MENU
echo 请选择内网穿透工具:
echo [1] Ngrok (推荐)
echo [2] Localtunnel
echo [3] Serveo
echo [4] 查看当前服务器状态
echo [5] 退出
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto NGROK
if "%choice%"=="2" goto LOCALTUNNEL
if "%choice%"=="3" goto SERVEO
if "%choice%"=="4" goto STATUS
if "%choice%"=="5" goto EXIT
echo 无效选择，请重试
goto MENU

:NGROK
echo.
echo 🚀 启动 Ngrok...
echo.
ngrok version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 ngrok
    echo 请先安装 ngrok: https://ngrok.com/download
    echo 或使用 npm 安装: npm install -g ngrok
    echo.
    pause
    goto MENU
)

echo ✅ 正在启动 ngrok 隧道...
echo 📝 复制下面的 HTTPS URL 用于外部服务配置
echo.
ngrok http 3000
goto END

:LOCALTUNNEL
echo.
echo 🚀 启动 Localtunnel...
echo.
npx localtunnel --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 正在安装 localtunnel...
    npm install -g localtunnel
)

echo ✅ 正在启动 localtunnel...
echo 📝 复制下面的 URL 用于外部服务配置
echo.
npx localtunnel --port 3000
goto END

:SERVEO
echo.
echo 🚀 启动 Serveo...
echo.
echo ✅ 正在启动 serveo 隧道...
echo 📝 复制下面的 URL 用于外部服务配置
echo.
ssh -R 80:localhost:3000 serveo.net
goto END

:STATUS
echo.
echo 📊 检查服务器状态...
echo.
curl -s http://localhost:3000/health 2>nul
if %errorlevel% neq 0 (
    echo ❌ 服务器未运行
    echo 请先启动 MCP HTTP Bridge: npm start
) else (
    echo ✅ 服务器正常运行
    echo 🌐 管理界面: http://localhost:3000
)
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 再见！
exit /b 0

:END
echo.
echo 🛑 隧道已关闭
pause
goto MENU

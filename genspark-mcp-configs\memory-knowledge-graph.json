{"name": "Memory Knowledge Graph", "description": "高级知识图谱记忆系统，支持复杂的知识存储和检索", "type": "StreamableHttp", "url": "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp", "headers": {"Content-Type": "application/json"}, "capabilities": {"tools": true, "resources": true, "prompts": true}, "features": ["知识图谱存储", "语义搜索", "关系映射", "上下文理解", "多维度检索"], "tools": [{"name": "add_memory", "description": "添加记忆到知识图谱", "parameters": {"content": {"type": "string", "description": "记忆内容", "required": true}, "context": {"type": "string", "description": "上下文信息", "required": false}, "tags": {"type": "array", "description": "标签列表", "items": {"type": "string"}, "required": false}}}, {"name": "search_memory", "description": "搜索知识图谱中的记忆", "parameters": {"query": {"type": "string", "description": "搜索查询", "required": true}, "limit": {"type": "number", "description": "结果数量限制", "default": 10, "required": false}, "similarity_threshold": {"type": "number", "description": "相似度阈值", "default": 0.7, "required": false}}}, {"name": "get_related_memories", "description": "获取相关记忆", "parameters": {"memory_id": {"type": "string", "description": "记忆ID", "required": true}, "depth": {"type": "number", "description": "关系深度", "default": 2, "required": false}}}, {"name": "update_memory", "description": "更新现有记忆", "parameters": {"memory_id": {"type": "string", "description": "记忆ID", "required": true}, "content": {"type": "string", "description": "新的内容", "required": false}, "tags": {"type": "array", "description": "新的标签", "items": {"type": "string"}, "required": false}}}, {"name": "delete_memory", "description": "删除记忆", "parameters": {"memory_id": {"type": "string", "description": "要删除的记忆ID", "required": true}}}], "examples": [{"description": "添加学习记录", "tool": "add_memory", "input": {"content": "学习了 React Hooks 的使用方法", "context": "前端开发学习", "tags": ["React", "<PERSON>s", "前端", "学习"]}}, {"description": "搜索技术相关记忆", "tool": "search_memory", "input": {"query": "React 开发", "limit": 5, "similarity_threshold": 0.8}}, {"description": "获取相关记忆", "tool": "get_related_memories", "input": {"memory_id": "memory_123", "depth": 2}}], "advanced_features": {"semantic_search": "支持语义搜索，理解查询意图", "knowledge_graph": "构建知识图谱，发现概念间关系", "context_awareness": "理解上下文，提供相关建议", "auto_tagging": "自动标签生成和分类", "similarity_matching": "基于相似度的智能匹配"}}
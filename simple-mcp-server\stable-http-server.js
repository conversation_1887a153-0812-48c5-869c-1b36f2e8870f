#!/usr/bin/env node

/**
 * 稳定的 HTTP MCP 服务器
 * 支持跨平台使用，持久连接，自动重连
 */

import express from 'express';
import cors from 'cors';
import { createServer } from 'http';

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json({ limit: '10mb' }));

// 简单的内存存储
const memories = [];
const notes = [];

// 工具定义
const tools = [
  {
    name: 'echo',
    description: 'Echo back the input text',
    inputSchema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: 'Text to echo back'
        }
      },
      required: ['text']
    }
  },
  {
    name: 'add',
    description: 'Add two numbers',
    inputSchema: {
      type: 'object',
      properties: {
        a: { type: 'number', description: 'First number' },
        b: { type: 'number', description: 'Second number' }
      },
      required: ['a', 'b']
    }
  },
  {
    name: 'remember',
    description: 'Store a memory or important information',
    inputSchema: {
      type: 'object',
      properties: {
        content: { type: 'string', description: 'The content to remember' },
        tags: { 
          type: 'array', 
          items: { type: 'string' }, 
          description: 'Optional tags for categorization' 
        }
      },
      required: ['content']
    }
  },
  {
    name: 'recall',
    description: 'Search and retrieve stored memories',
    inputSchema: {
      type: 'object',
      properties: {
        query: { type: 'string', description: 'Search query to find relevant memories' },
        limit: { type: 'number', description: 'Maximum number of results to return', default: 5 }
      },
      required: ['query']
    }
  },
  {
    name: 'add_note',
    description: 'Add a quick note',
    inputSchema: {
      type: 'object',
      properties: {
        title: { type: 'string', description: 'Note title' },
        content: { type: 'string', description: 'Note content' }
      },
      required: ['title', 'content']
    }
  },
  {
    name: 'list_notes',
    description: 'List all stored notes',
    inputSchema: {
      type: 'object',
      properties: {
        limit: { type: 'number', description: 'Maximum number of notes to return', default: 10 }
      }
    }
  },
  {
    name: 'get_time',
    description: 'Get current date and time',
    inputSchema: {
      type: 'object',
      properties: {
        format: { 
          type: 'string', 
          description: 'Time format: iso, local, or timestamp',
          default: 'local'
        }
      }
    }
  }
];

// 工具处理函数
function handleToolCall(name, args) {
  switch (name) {
    case 'echo':
      return {
        content: [{ type: 'text', text: `Echo: ${args.text}` }]
      };

    case 'add':
      const result = args.a + args.b;
      return {
        content: [{ type: 'text', text: `${args.a} + ${args.b} = ${result}` }]
      };

    case 'remember':
      const memory = {
        id: Date.now(),
        content: args.content,
        tags: args.tags || [],
        timestamp: new Date().toISOString()
      };
      memories.push(memory);
      return {
        content: [{
          type: 'text',
          text: `✅ 记忆已保存 (ID: ${memory.id})\n内容: ${memory.content}\n标签: ${memory.tags.join(', ') || '无'}`
        }]
      };

    case 'recall':
      const query = args.query.toLowerCase();
      const limit = args.limit || 5;
      const matchingMemories = memories
        .filter(memory => 
          memory.content.toLowerCase().includes(query) ||
          memory.tags.some(tag => tag.toLowerCase().includes(query))
        )
        .slice(0, limit);

      if (matchingMemories.length === 0) {
        return {
          content: [{ type: 'text', text: `❌ 没有找到与 "${args.query}" 相关的记忆` }]
        };
      }

      const resultText = matchingMemories
        .map(memory => `📝 ID: ${memory.id}\n内容: ${memory.content}\n标签: ${memory.tags.join(', ') || '无'}\n时间: ${memory.timestamp}`)
        .join('\n\n');

      return {
        content: [{ type: 'text', text: `🔍 找到 ${matchingMemories.length} 条相关记忆:\n\n${resultText}` }]
      };

    case 'add_note':
      const note = {
        id: Date.now(),
        title: args.title,
        content: args.content,
        timestamp: new Date().toISOString()
      };
      notes.push(note);
      return {
        content: [{
          type: 'text',
          text: `📝 笔记已添加 (ID: ${note.id})\n标题: ${note.title}\n内容: ${note.content}`
        }]
      };

    case 'list_notes':
      const limit2 = args.limit || 10;
      const recentNotes = notes.slice(-limit2).reverse();

      if (recentNotes.length === 0) {
        return { content: [{ type: 'text', text: '📝 暂无笔记' }] };
      }

      const notesText = recentNotes
        .map(note => `📝 ID: ${note.id}\n标题: ${note.title}\n内容: ${note.content}\n时间: ${note.timestamp}`)
        .join('\n\n');

      return {
        content: [{ type: 'text', text: `📝 最近的 ${recentNotes.length} 条笔记:\n\n${notesText}` }]
      };

    case 'get_time':
      const now = new Date();
      let timeText;
      
      switch (args.format) {
        case 'iso':
          timeText = now.toISOString();
          break;
        case 'timestamp':
          timeText = now.getTime().toString();
          break;
        default:
          timeText = now.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
      }

      return {
        content: [{ type: 'text', text: `🕐 当前时间: ${timeText}` }]
      };

    default:
      throw new Error(`Unknown tool: ${name}`);
  }
}

// MCP 消息处理
function handleMCPMessage(message) {
  const { method, params, id } = message;

  switch (method) {
    case 'initialize':
      return {
        jsonrpc: '2.0',
        id,
        result: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {},
            resources: {},
            prompts: {},
            logging: {}
          },
          serverInfo: {
            name: 'stable-mcp-server',
            version: '1.0.0'
          }
        }
      };

    case 'initialized':
      // Cherry Studio 发送的初始化完成通知
      return {
        jsonrpc: '2.0',
        id,
        result: {}
      };

    case 'tools/list':
      return {
        jsonrpc: '2.0',
        id,
        result: { tools }
      };

    case 'tools/call':
      try {
        const result = handleToolCall(params.name, params.arguments);
        return { jsonrpc: '2.0', id, result };
      } catch (error) {
        return {
          jsonrpc: '2.0',
          id,
          error: { code: -32000, message: error.message }
        };
      }

    case 'resources/list':
      // 资源列表（Cherry Studio 可能需要）
      return {
        jsonrpc: '2.0',
        id,
        result: { resources: [] }
      };

    case 'prompts/list':
      // 提示列表（Cherry Studio 可能需要）
      return {
        jsonrpc: '2.0',
        id,
        result: { prompts: [] }
      };

    case 'mcp-restart-server':
      // Cherry Studio 特有的重启服务器方法
      console.log('🔄 Cherry Studio 请求重启服务器');
      return {
        jsonrpc: '2.0',
        id,
        result: {
          status: 'restarted',
          message: 'Server restarted successfully',
          timestamp: new Date().toISOString()
        }
      };

    case 'ping':
      // 心跳检测
      return {
        jsonrpc: '2.0',
        id,
        result: {
          status: 'pong',
          timestamp: new Date().toISOString()
        }
      };

    default:
      console.log(`⚠️  未知方法: ${method}`, params);
      return {
        jsonrpc: '2.0',
        id,
        error: { code: -32601, message: `Method not found: ${method}` }
      };
  }
}

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    tools: tools.length,
    memories: memories.length,
    notes: notes.length
  });
});

// 服务器信息端点
app.get('/mcp', (req, res) => {
  res.json({
    name: 'stable-mcp-server',
    version: '1.0.0',
    protocol: 'MCP 2024-11-05',
    status: 'running',
    tools: tools.length,
    endpoint: 'POST /mcp for MCP requests',
    features: ['cross-platform', 'persistent', 'auto-reconnect']
  });
});

// 主要的 MCP 端点
app.post('/mcp', (req, res) => {
  try {
    const response = handleMCPMessage(req.body);
    res.json(response);
  } catch (error) {
    console.error('MCP Error:', error);
    res.status(500).json({
      jsonrpc: '2.0',
      id: req.body.id || null,
      error: {
        code: -32603,
        message: 'Internal error',
        data: error.message
      }
    });
  }
});

// 启动服务器
const server = createServer(app);

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 稳定的 MCP 服务器启动成功!`);
  console.log(`📍 本地访问: http://localhost:${PORT}/mcp`);
  console.log(`🌐 网络访问: http://0.0.0.0:${PORT}/mcp`);
  console.log(`💊 健康检查: http://localhost:${PORT}/health`);
  console.log(`🛠️  工具数量: ${tools.length}`);
  console.log(`⚡ 支持跨平台使用`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🔚 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🔚 收到终止信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

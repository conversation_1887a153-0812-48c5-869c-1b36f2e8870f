<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外部服务配置向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 40px;
        }
        
        .step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }
        
        .step.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .step-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .step-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            line-height: 40px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .step-title {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .step-description {
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }
        
        .service-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .service-card {
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .service-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }
        
        .service-card.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .service-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .service-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .service-description {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .config-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .progress-bar {
            height: 4px;
            background: #ecf0f1;
            border-radius: 2px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .copy-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 外部服务配置向导</h1>
            <p>轻松配置 Coze、GenSpark 等服务访问您的本地 MCP 服务器</p>
        </div>
        
        <div class="content">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 25%"></div>
            </div>
            
            <!-- 步骤 1: 选择服务 -->
            <div class="step active" id="step1">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <div class="step-title">选择外部服务</div>
                    <div class="step-description">请选择您要配置的外部服务</div>
                </div>
                
                <div class="service-cards">
                    <div class="service-card" data-service="coze">
                        <div class="service-icon">🤖</div>
                        <div class="service-name">Coze</div>
                        <div class="service-description">字节跳动的 AI 助手平台</div>
                    </div>
                    
                    <div class="service-card" data-service="genspark">
                        <div class="service-icon">⚡</div>
                        <div class="service-name">GenSpark</div>
                        <div class="service-description">AI 搜索和生成平台</div>
                    </div>
                    
                    <div class="service-card" data-service="custom">
                        <div class="service-icon">🔧</div>
                        <div class="service-name">自定义服务</div>
                        <div class="service-description">其他支持 HTTP API 的服务</div>
                    </div>
                </div>
                
                <div class="buttons">
                    <div></div>
                    <button class="btn btn-primary" id="nextStep1" disabled>下一步</button>
                </div>
            </div>
            
            <!-- 步骤 2: 输入隧道 URL -->
            <div class="step" id="step2">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <div class="step-title">配置内网穿透</div>
                    <div class="step-description">输入您的内网穿透 URL</div>
                </div>
                
                <div class="alert alert-info">
                    <strong>提示:</strong> 请先启动内网穿透工具 (如 ngrok)，然后输入生成的公网 URL
                </div>
                
                <div class="form-group">
                    <label for="tunnelUrl">内网穿透 URL:</label>
                    <input type="url" id="tunnelUrl" placeholder="https://abc123.ngrok.io" required>
                </div>
                
                <div class="form-group">
                    <label>常用内网穿透工具:</label>
                    <div style="margin-top: 10px;">
                        <button type="button" class="btn btn-secondary" onclick="showTunnelHelp('ngrok')">Ngrok</button>
                        <button type="button" class="btn btn-secondary" onclick="showTunnelHelp('localtunnel')">Localtunnel</button>
                        <button type="button" class="btn btn-secondary" onclick="showTunnelHelp('serveo')">Serveo</button>
                    </div>
                </div>
                
                <div id="tunnelHelp" class="config-output" style="display: none;"></div>
                
                <div class="buttons">
                    <button class="btn btn-secondary" onclick="previousStep()">上一步</button>
                    <button class="btn btn-primary" id="nextStep2" onclick="nextStep()">下一步</button>
                </div>
            </div>
            
            <!-- 步骤 3: 生成配置 -->
            <div class="step" id="step3">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <div class="step-title">生成配置</div>
                    <div class="step-description">为您的服务生成配置信息</div>
                </div>
                
                <div id="configContent"></div>
                
                <div class="buttons">
                    <button class="btn btn-secondary" onclick="previousStep()">上一步</button>
                    <button class="btn btn-primary" onclick="nextStep()">完成</button>
                </div>
            </div>
            
            <!-- 步骤 4: 完成 -->
            <div class="step" id="step4">
                <div class="step-header">
                    <div class="step-number">✓</div>
                    <div class="step-title">配置完成</div>
                    <div class="step-description">您的配置已生成完毕</div>
                </div>
                
                <div class="alert alert-info">
                    <strong>恭喜!</strong> 您已成功配置外部服务访问。现在可以在您选择的平台中使用这些配置了。
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <button class="btn btn-primary" onclick="location.href='/'">返回主页</button>
                    <button class="btn btn-secondary" onclick="restartWizard()">重新配置</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="config-wizard.js"></script>
</body>
</html>

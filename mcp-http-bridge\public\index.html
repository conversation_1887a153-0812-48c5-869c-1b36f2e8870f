<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP HTTP Bridge 管理界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .status-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .status-card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-indicator.online {
            background: #27ae60;
        }
        
        .status-indicator.offline {
            background: #e74c3c;
        }
        
        .server-list {
            display: grid;
            gap: 20px;
        }
        
        .server-item {
            border: 1px solid #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        
        .server-name {
            font-weight: bold;
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .server-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .info-label {
            font-weight: 500;
            color: #7f8c8d;
        }
        
        .info-value {
            color: #2c3e50;
        }
        
        .tools-section {
            margin-top: 20px;
        }
        
        .tools-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .tool-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ecf0f1;
        }
        
        .tool-name {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .tool-description {
            font-size: 14px;
            color: #7f8c8d;
        }
        
        .api-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .api-endpoint {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #3498db;
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .method.get {
            background: #27ae60;
            color: white;
        }
        
        .method.post {
            background: #e67e22;
            color: white;
        }
        
        .endpoint-url {
            font-family: 'Courier New', monospace;
            color: #2c3e50;
        }
        
        .test-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .form-group select,
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        
        .config-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .config-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #9b59b6;
        }
        
        .config-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .config-url {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌉 MCP HTTP Bridge</h1>
            <p>将本地 MCP 服务器暴露为 HTTP API，支持内网穿透访问</p>
            <div style="margin-top: 20px;">
                <a href="config-wizard.html" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; border-radius: 6px; text-decoration: none; display: inline-block; margin-right: 10px;">🔧 配置向导</a>
                <a href="#api-section" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; border-radius: 6px; text-decoration: none; display: inline-block;">📡 API 文档</a>
            </div>
        </div>

        <div class="status-card">
            <h2>
                <span class="status-indicator" id="statusIndicator"></span>
                服务器状态
            </h2>
            <div id="serverStatus">
                <div class="loading">正在加载状态信息...</div>
            </div>
        </div>

        <div class="config-section">
            <h2>🔗 外部访问配置</h2>
            <p style="margin-bottom: 20px; color: #7f8c8d;">
                以下是为 Coze 和 GenSpark 等外部服务配置的 API 端点。请确保已启动内网穿透工具。
            </p>
            
            <div class="config-item">
                <div class="config-title">🤖 Coze 配置</div>
                <p style="margin-bottom: 10px; color: #7f8c8d;">在 Coze 中添加以下 API 端点：</p>
                <div class="config-url" id="cozeConfig">
                    等待内网穿透 URL...
                </div>
            </div>
            
            <div class="config-item">
                <div class="config-title">⚡ GenSpark 配置</div>
                <p style="margin-bottom: 10px; color: #7f8c8d;">在 GenSpark 中使用以下配置：</p>
                <div class="config-url" id="gensparkConfig">
                    等待内网穿透 URL...
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>📡 API 端点</h2>
            
            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-url">/health</span>
                <p>检查服务器健康状态</p>
            </div>
            
            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-url">/api/servers</span>
                <p>获取所有可用的 MCP 服务器列表</p>
            </div>
            
            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-url">/api/servers/{serverName}/tools</span>
                <p>获取指定服务器的工具列表</p>
            </div>
            
            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="endpoint-url">/api/servers/{serverName}/tools/{toolName}</span>
                <p>调用指定服务器的特定工具</p>
            </div>
            
            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="endpoint-url">/api/call</span>
                <p>通用工具调用接口（推荐用于外部服务）</p>
            </div>

            <div class="test-form">
                <h3>🧪 API 测试</h3>
                <form id="testForm">
                    <div class="form-group">
                        <label for="serverSelect">选择服务器:</label>
                        <select id="serverSelect" required>
                            <option value="">请选择...</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="toolSelect">选择工具:</label>
                        <select id="toolSelect" required>
                            <option value="">请先选择服务器...</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="argsInput">参数 (JSON 格式):</label>
                        <textarea id="argsInput" placeholder='{"key": "value"}'>{}</textarea>
                    </div>
                    
                    <button type="submit" class="btn">调用工具</button>
                </form>
                
                <div id="testResult"></div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

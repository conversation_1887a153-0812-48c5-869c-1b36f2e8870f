import express from 'express';

// 简单的工具数据
const tools = [
  {
    name: 'echo',
    description: 'Echo back the input text',
    inputSchema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: 'Text to echo back'
        }
      },
      required: ['text']
    }
  },
  {
    name: 'add',
    description: 'Add two numbers',
    inputSchema: {
      type: 'object',
      properties: {
        a: {
          type: 'number',
          description: 'First number'
        },
        b: {
          type: 'number',
          description: 'Second number'
        }
      },
      required: ['a', 'b']
    }
  }
];

// 处理工具调用的函数
function handleToolCall(name, args) {
  switch (name) {
    case 'echo':
      return {
        content: [
          {
            type: 'text',
            text: `Echo: ${args.text}`
          }
        ]
      };

    case 'add':
      const result = args.a + args.b;
      return {
        content: [
          {
            type: 'text',
            text: `${args.a} + ${args.b} = ${result}`
          }
        ]
      };

    default:
      throw new Error(`Unknown tool: ${name}`);
  }
}

// 创建 Express 应用
const app = express();
app.use(express.json());

// 存储会话状态
const sessions = new Map();

// MCP HTTP 端点
app.post('/mcp', async (req, res) => {
  try {
    const request = req.body;
    
    // 验证 JSON-RPC 格式
    if (!request || request.jsonrpc !== '2.0') {
      return res.status(400).json({
        jsonrpc: '2.0',
        id: request?.id || null,
        error: {
          code: -32600,
          message: 'Invalid Request'
        }
      });
    }

    // 处理初始化
    if (request.method === 'initialize') {
      const sessionId = req.headers['x-session-id'] || 'default';
      sessions.set(sessionId, { initialized: true });
      
      return res.json({
        jsonrpc: '2.0',
        id: request.id,
        result: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {},
            resources: {},
            prompts: {}
          },
          serverInfo: {
            name: 'simple-mcp-server',
            version: '1.0.0'
          }
        }
      });
    }

    // 处理工具列表
    if (request.method === 'tools/list') {
      return res.json({
        jsonrpc: '2.0',
        id: request.id,
        result: {
          tools: tools
        }
      });
    }

    // 处理工具调用
    if (request.method === 'tools/call') {
      const { name, arguments: args } = request.params;
      const callResponse = handleToolCall(name, args);
      return res.json({
        jsonrpc: '2.0',
        id: request.id,
        result: callResponse
      });
    }

    // 未知方法
    return res.status(404).json({
      jsonrpc: '2.0',
      id: request.id,
      error: {
        code: -32601,
        message: `Method not found: ${request.method}`
      }
    });

  } catch (error) {
    console.error('MCP endpoint error:', error);
    return res.status(500).json({
      jsonrpc: '2.0',
      id: req.body?.id || null,
      error: {
        code: -32603,
        message: 'Internal error'
      }
    });
  }
});

// GET 端点 - 返回 405 Method Not Allowed
app.get('/mcp', (req, res) => {
  res.status(405).set('Allow', 'POST').send('Method Not Allowed');
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`🚀 Simple MCP Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 MCP endpoint: http://localhost:${PORT}/mcp`);
});

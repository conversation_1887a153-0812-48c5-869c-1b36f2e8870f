# 🚀 快速开始指南

## 当前状态
✅ MCP HTTP Bridge 服务器已启动 (http://localhost:3000)
✅ Memory MCP 服务器正常运行
⚠️ Notion MCP 服务器需要配置 (可选)

## 立即开始使用

### 1. 启动内网穿透

选择以下任一方式：

#### 方式 A: 使用 Ngrok (推荐)
```bash
# 如果没有安装 ngrok，先安装
npm install -g ngrok

# 启动隧道
ngrok http 3000
```

#### 方式 B: 使用批处理脚本
```bash
setup-tunnel.bat
```

#### 方式 C: 使用 Localtunnel
```bash
npx localtunnel --port 3000
```

### 2. 获取公网 URL

从内网穿透工具的输出中复制 HTTPS URL，例如：
- Ngrok: `https://abc123.ngrok.io`
- Localtunnel: `https://funny-cat-123.loca.lt`

### 3. 配置外部服务

访问配置向导：http://localhost:3000/config-wizard.html

或手动配置：

#### Coze 配置
- API 端点: `https://your-tunnel-url.ngrok.io/api/call`
- 方法: POST
- 请求体:
```json
{
  "server": "memory",
  "tool": "search_memory",
  "args": {"query": "搜索内容"}
}
```

#### GenSpark 配置
- Base URL: `https://your-tunnel-url.ngrok.io`
- API 端点: `/api/call`
- 健康检查: `/health`

### 4. 测试连接

```bash
# 测试健康状态
curl https://your-tunnel-url.ngrok.io/health

# 测试 API 调用
curl -X POST https://your-tunnel-url.ngrok.io/api/call \
  -H "Content-Type: application/json" \
  -d '{"server": "memory", "tool": "add_memory", "args": {"content": "测试信息"}}'
```

## 🔧 可用的 API 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/health` | GET | 检查服务器状态 |
| `/api/servers` | GET | 获取可用服务器列表 |
| `/api/call` | POST | 通用工具调用 |

## 🧠 Memory 服务器工具

当前可用的 Memory MCP 工具：
- `add_memory`: 添加记忆
- `search_memory`: 搜索记忆
- `get_memory`: 获取记忆
- `update_memory`: 更新记忆
- `delete_memory`: 删除记忆

## 📝 使用示例

### 添加记忆
```json
{
  "server": "memory",
  "tool": "add_memory",
  "args": {
    "content": "重要的项目信息",
    "tags": ["工作", "项目"]
  }
}
```

### 搜索记忆
```json
{
  "server": "memory",
  "tool": "search_memory",
  "args": {
    "query": "项目"
  }
}
```

## 🔒 安全提示

1. 内网穿透 URL 是公开的，请注意保护敏感信息
2. 可以在 ngrok 配置中添加基本认证
3. 生产环境建议使用专业的内网穿透服务

## 🆘 故障排除

### 问题：无法访问内网穿透 URL
- 检查防火墙设置
- 确认本地服务器正在运行
- 尝试重新启动内网穿透工具

### 问题：API 调用失败
- 访问 `/health` 检查服务器状态
- 确认请求格式正确
- 查看浏览器控制台错误信息

### 问题：工具调用超时
- 检查 MCP 服务器状态
- 确认工具名称正确
- 增加请求超时时间

## 📞 获取帮助

1. 访问管理界面: http://localhost:3000
2. 使用配置向导: http://localhost:3000/config-wizard.html
3. 查看详细文档: README.md 和 USAGE.md

---

**🎉 恭喜！您的 MCP HTTP Bridge 系统已经准备就绪！**

现在您可以在 Coze、GenSpark 或任何支持 HTTP API 的服务中使用您的本地 MCP 服务器了。

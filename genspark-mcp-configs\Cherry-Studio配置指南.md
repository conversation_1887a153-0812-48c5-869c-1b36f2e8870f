# 🍒 Cherry Studio MCP 配置指南

## 🔍 问题分析

您遇到的问题很常见！不同的 MCP 客户端对协议的实现略有差异：

### 客户端差异对比

| 特性 | GenSpark | Cherry Studio |
|------|----------|---------------|
| 协议支持 | 基础 MCP | 扩展 MCP |
| 必需方法 | initialize, tools/* | + mcp-restart-server |
| 能力声明 | 简单 | 完整 (resources, prompts, logging) |
| 错误处理 | 宽松 | 严格 |
| 重连机制 | 基础 | 高级 |

## ✅ 解决方案

我已经更新了服务器以完全支持 Cherry Studio！

### 新增的 Cherry Studio 支持

1. **mcp-restart-server** - Cherry Studio 特有的重启方法
2. **完整能力声明** - resources, prompts, logging
3. **initialized 方法** - 初始化完成通知
4. **ping/pong** - 心跳检测
5. **resources/list** - 资源列表
6. **prompts/list** - 提示列表

## 🚀 配置步骤

### 1. 确认服务器运行
服务器应该显示：
```
🚀 稳定的 MCP 服务器启动成功!
📍 本地访问: http://localhost:3001/mcp
🛠️  工具数量: 7
⚡ 支持跨平台使用
```

### 2. Cherry Studio 配置

**配置文件**: `.mcp.json`
```json
{
  "mcpServers": {
    "stable-mcp-server": {
      "type": "http",
      "url": "http://localhost:3001/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

### 3. 验证配置

在 Cherry Studio 中应该能看到：
- ✅ 服务器连接成功
- ✅ 7个工具可用
- ✅ 无连接错误

## 🛠️ 支持的方法

### 核心 MCP 方法
- `initialize` - 初始化连接
- `initialized` - 初始化完成通知
- `tools/list` - 获取工具列表
- `tools/call` - 调用工具

### Cherry Studio 扩展方法
- `mcp-restart-server` - 重启服务器
- `resources/list` - 获取资源列表
- `prompts/list` - 获取提示列表
- `ping` - 心跳检测

### 工具列表（7个）
1. **echo** - 文本回显
2. **add** - 数字加法
3. **remember** - 存储记忆
4. **recall** - 搜索记忆
5. **add_note** - 添加笔记
6. **list_notes** - 列出笔记
7. **get_time** - 获取时间

## 🔧 故障排除

### 常见问题

**问题1**: "Error invoking remote method 'mcp-restart-server'"
- ✅ **已解决**: 服务器现在支持此方法

**问题2**: "Bad Gateway (502)"
- 🔍 **检查**: 确认服务器正在运行
- 🔧 **解决**: 重启服务器

**问题3**: 连接超时
- 🔍 **检查**: URL 是否正确 `http://localhost:3001/mcp`
- 🔧 **解决**: 确认端口 3001 未被占用

### 调试步骤

1. **检查服务器状态**:
   ```bash
   curl http://localhost:3001/health
   ```

2. **测试 mcp-restart-server**:
   ```bash
   curl -X POST http://localhost:3001/mcp \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc": "2.0", "method": "mcp-restart-server", "params": {}, "id": 1}'
   ```

3. **查看服务器日志**:
   服务器会显示所有请求和未知方法

## 🎯 测试验证

### 在 Cherry Studio 中测试

1. **连接测试**: 服务器应该显示为在线
2. **工具测试**: 尝试使用 echo 工具
3. **记忆测试**: 使用 remember 和 recall 工具
4. **时间测试**: 使用 get_time 工具

### 预期结果

```
用户: "请回显：Cherry Studio 测试"
AI: 使用 echo 工具...
结果: Echo: Cherry Studio 测试

用户: "帮我记住：Cherry Studio 配置成功"
AI: 使用 remember 工具...
结果: ✅ 记忆已保存 (ID: **********)
内容: Cherry Studio 配置成功
标签: 无

用户: "现在几点了？"
AI: 使用 get_time 工具...
结果: 🕐 当前时间: 2025/7/16 23:51:16
```

## 🔄 重启服务器

如果需要重启服务器：

**方法1**: Cherry Studio 内置重启
- Cherry Studio 会自动调用 `mcp-restart-server` 方法

**方法2**: 手动重启
```bash
# 停止服务器 (Ctrl+C)
# 重新启动
cd D:\Codefield\simple-mcp-server
node stable-http-server.js
```

**方法3**: 使用批处理文件
```bash
D:\Codefield\simple-mcp-server\start-server.bat
```

## 🌟 Cherry Studio 特有功能

### 1. 自动重连
Cherry Studio 会自动检测连接状态并重连

### 2. 服务器重启
Cherry Studio 可以远程重启 MCP 服务器

### 3. 完整协议支持
支持 MCP 协议的所有扩展功能

### 4. 错误恢复
更好的错误处理和恢复机制

## 🎉 成功标志

当您看到以下情况时，说明配置成功：

- ✅ Cherry Studio 中服务器状态为"在线"
- ✅ 可以看到 7 个可用工具
- ✅ 工具调用正常响应
- ✅ 无 502 或连接错误
- ✅ 服务器日志显示正常请求

## 📞 技术支持

如果仍有问题，请检查：

1. **服务器是否运行**: `http://localhost:3001/health`
2. **端口是否冲突**: 尝试更改端口
3. **防火墙设置**: 确保端口 3001 开放
4. **Cherry Studio 版本**: 确保使用最新版本

现在 Cherry Studio 应该能完美工作了！🎉

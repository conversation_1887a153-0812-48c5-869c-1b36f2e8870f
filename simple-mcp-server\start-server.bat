@echo off
echo 🚀 启动稳定的 MCP 服务器...
echo.

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo 🔍 检查 Node.js...

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
echo.

echo 🔍 检查服务器文件...
if not exist "stable-http-server.js" (
    echo ❌ 错误: 未找到 stable-http-server.js 文件
    pause
    exit /b 1
)

echo ✅ 服务器文件存在
echo.

echo 🚀 启动服务器...
echo 📝 提示: 按 Ctrl+C 停止服务器
echo.

node stable-http-server.js

echo.
echo 🔚 服务器已停止
pause

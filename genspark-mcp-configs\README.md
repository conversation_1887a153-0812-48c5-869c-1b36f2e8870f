# GenSpark MCP 配置包

🎉 欢迎使用 GenSpark MCP 配置包！这个包包含了两个功能强大的 MCP 服务器配置。

## 📦 包含内容

```
genspark-mcp-configs/
├── README.md                    # 本文件
├── 导入指南.md                   # 详细导入指南
├── simple-mcp-server.json       # 简单 MCP 服务器配置
├── memory-knowledge-graph.json  # 知识图谱服务器配置
└── test-scripts/                # 测试脚本
    ├── test-simple.ps1          # 测试简单服务器
    └── test-memory.ps1          # 测试知识图谱服务器
```

## 🚀 快速开始

### 1. 选择服务器

#### 🔰 新手推荐：Simple MCP Server
- **功能**: 记忆存储、笔记管理、基础工具
- **特点**: 简单易用、稳定可靠
- **适用**: 日常使用、学习入门

#### 🔥 高级用户：Memory Knowledge Graph
- **功能**: 知识图谱、语义搜索、关系映射
- **特点**: 功能强大、专业级
- **适用**: 复杂知识管理、专业用途

### 2. 配置信息

#### Simple MCP Server
```json
{
  "name": "Simple MCP Server",
  "type": "StreamableHttp",
  "url": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp",
  "headers": {"Content-Type": "application/json"}
}
```

#### Memory Knowledge Graph
```json
{
  "name": "Memory Knowledge Graph",
  "type": "StreamableHttp", 
  "url": "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp",
  "headers": {"Content-Type": "application/json"}
}
```

### 3. 添加到 GenSpark

1. 打开 GenSpark
2. 进入 MCP 服务器设置
3. 点击"添加新服务器"
4. 复制上述配置信息
5. 保存并测试连接

## 🛠️ 可用工具

### Simple MCP Server 工具

| 工具名 | 功能 | 用途 |
|--------|------|------|
| `echo` | 文本回显 | 测试连接 |
| `add` | 数字加法 | 数学计算 |
| `remember` | 存储记忆 | 保存重要信息 |
| `recall` | 搜索记忆 | 查找已存储内容 |
| `add_note` | 添加笔记 | 创建结构化笔记 |
| `list_notes` | 列出笔记 | 查看笔记列表 |

### Memory Knowledge Graph 工具

| 工具名 | 功能 | 用途 |
|--------|------|------|
| `add_memory` | 添加记忆 | 知识图谱存储 |
| `search_memory` | 搜索记忆 | 语义搜索 |
| `get_related_memories` | 获取相关记忆 | 关系发现 |
| `update_memory` | 更新记忆 | 内容修改 |
| `delete_memory` | 删除记忆 | 内容清理 |

## 📝 使用示例

### 基础使用
```
用户: "帮我记住：明天下午3点有重要会议"
AI: 使用 remember 工具存储...
结果: ✅ 记忆已保存
```

### 搜索功能
```
用户: "帮我找找关于会议的记忆"
AI: 使用 recall 工具搜索...
结果: 🔍 找到相关记忆...
```

### 笔记管理
```
用户: "添加笔记：周末学习计划"
AI: 使用 add_note 工具...
结果: 📝 笔记已添加
```

## 🔧 配置方式

### 方式一：手动配置（推荐）
1. 复制配置信息
2. 在 GenSpark 中手动填写
3. 测试连接

### 方式二：JSON 导入（如果支持）
1. 下载 JSON 配置文件
2. 在 GenSpark 中导入
3. 验证配置

### 方式三：批量配置
1. 使用提供的测试脚本
2. 验证服务器状态
3. 批量添加配置

## ✅ 验证配置

配置完成后，测试以下功能：

1. **连接测试**: `echo "Hello MCP"`
2. **记忆功能**: `remember "测试记忆"`
3. **搜索功能**: `recall "测试"`
4. **笔记功能**: `add_note "测试笔记"`

## 🆘 故障排除

### 常见问题

1. **无法连接**
   - 检查 URL 是否正确
   - 确认服务器运行状态
   - 验证网络连接

2. **工具不响应**
   - 重新初始化连接
   - 检查参数格式
   - 查看错误日志

3. **功能异常**
   - 重启 MCP 服务器
   - 清除缓存重试
   - 检查服务器日志

### 获取帮助

1. 查看 `导入指南.md` 获取详细说明
2. 运行测试脚本检查服务器状态
3. 检查服务器控制台日志

## 🎯 最佳实践

1. **新手建议**: 先配置 Simple MCP Server
2. **高级用户**: 两个服务器都配置，功能互补
3. **定期备份**: 重要记忆和笔记要备份
4. **合理使用**: 根据需求选择合适的工具

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交改进建议和问题反馈！

---

**开始您的 MCP 之旅吧！** 🚀

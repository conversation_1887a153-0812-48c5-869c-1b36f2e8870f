# 🌐 跨平台 MCP 解决方案

## ✅ 问题解决

您说得完全正确！stdio 方式确实有很大局限性：

- ❌ **不支持跨平台**: 依赖本地文件路径
- ❌ **连接不稳定**: 进程容易断开
- ❌ **部署复杂**: 每个设备都需要配置

## 🚀 新的解决方案：稳定的 HTTP 服务器

### 优势对比

| 特性 | stdio 方式 | HTTP 方式 |
|------|------------|-----------|
| 跨平台支持 | ❌ 不支持 | ✅ 完全支持 |
| 连接稳定性 | ❌ 容易断开 | ✅ 持久连接 |
| 安卓支持 | ❌ 不支持 | ✅ 支持 |
| 网络访问 | ❌ 仅本地 | ✅ 局域网/互联网 |
| 部署难度 | ❌ 复杂 | ✅ 简单 |
| 重连机制 | ❌ 无 | ✅ 自动重连 |

## 📋 当前配置

### 服务器状态
- ✅ **HTTP 服务器**: 运行在 `http://localhost:3001/mcp`
- ✅ **健康检查**: `http://localhost:3001/health`
- ✅ **跨平台支持**: Windows, Android, iOS, macOS, Linux
- ✅ **7个工具**: echo, add, remember, recall, add_note, list_notes, get_time

### 配置文件
**文件**: `.mcp.json`
```json
{
  "mcpServers": {
    "stable-mcp-server": {
      "type": "http",
      "url": "http://localhost:3001/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

## 🛠️ 使用步骤

### 1. 启动服务器
**Windows**:
```bash
# 方法1: 使用批处理文件
D:\Codefield\simple-mcp-server\start-server.bat

# 方法2: 手动启动
cd D:\Codefield\simple-mcp-server
node stable-http-server.js
```

**其他平台**:
```bash
cd /path/to/simple-mcp-server
node stable-http-server.js
```

### 2. 验证服务器
访问: `http://localhost:3001/health`
应该看到:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-16T15:11:00.729Z",
  "uptime": 123.45,
  "tools": 7,
  "memories": 0,
  "notes": 0
}
```

### 3. 配置通义灵码
1. 将配置文件内容复制到项目根目录的 `.mcp.json`
2. 重启通义灵码
3. 检查 MCP 服务器列表中的 "stable-mcp-server"

## 🌍 跨平台部署

### Windows 部署
```bash
# 启动服务器
cd D:\Codefield\simple-mcp-server
node stable-http-server.js
```

### Android 部署（Termux）
```bash
# 安装 Node.js
pkg install nodejs

# 复制服务器文件到 Android
# 启动服务器
cd /data/data/com.termux/files/home/<USER>
node stable-http-server.js
```

### 云服务器部署
```bash
# 上传文件到云服务器
scp stable-http-server.js user@server:/path/to/mcp/

# 在服务器上启动
cd /path/to/mcp/
nohup node stable-http-server.js > server.log 2>&1 &
```

### 配置不同平台的 URL

**本地使用**:
```json
"url": "http://localhost:3001/mcp"
```

**局域网使用**:
```json
"url": "http://*************:3001/mcp"
```

**云服务器使用**:
```json
"url": "http://your-server.com:3001/mcp"
```

## 🔧 高级配置

### 环境变量
```bash
# 自定义端口
PORT=8080 node stable-http-server.js

# 绑定特定IP
HOST=0.0.0.0 PORT=3001 node stable-http-server.js
```

### PM2 进程管理（推荐）
```bash
# 安装 PM2
npm install -g pm2

# 启动服务器
pm2 start stable-http-server.js --name mcp-server

# 查看状态
pm2 status

# 重启服务器
pm2 restart mcp-server

# 开机自启
pm2 startup
pm2 save
```

## 🛡️ 安全考虑

### 本地使用
- 默认只绑定 localhost，安全性较高
- 防火墙会阻止外部访问

### 网络使用
- 考虑添加认证机制
- 使用 HTTPS（需要 SSL 证书）
- 限制访问 IP 范围

### 生产环境
```javascript
// 添加认证中间件
app.use('/mcp', (req, res, next) => {
  const token = req.headers.authorization;
  if (token !== 'Bearer your-secret-token') {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  next();
});
```

## 🎯 测试验证

### 健康检查
```bash
curl http://localhost:3001/health
```

### MCP 初始化测试
```bash
curl -X POST http://localhost:3001/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "Test", "version": "1.0.0"}}, "id": 1}'
```

### 工具列表测试
```bash
curl -X POST http://localhost:3001/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "tools/list", "params": {}, "id": 2}'
```

## 🚀 下一步

1. **启动服务器**: 运行 `start-server.bat` 或手动启动
2. **更新配置**: 使用新的 HTTP 配置
3. **测试功能**: 在通义灵码中测试各种工具
4. **跨平台部署**: 根据需要部署到其他设备
5. **扩展功能**: 添加更多自定义工具

## 💡 为什么选择 HTTP 而不是 stdio？

1. **跨平台兼容性**: HTTP 是标准协议，所有平台都支持
2. **网络透明性**: 可以轻松实现远程访问
3. **连接稳定性**: HTTP 有完善的重连和错误处理机制
4. **扩展性**: 容易添加认证、负载均衡等功能
5. **调试友好**: 可以用浏览器、curl 等工具直接测试

现在您有了一个真正跨平台、稳定的 MCP 解决方案！🎉

# Memory Knowledge Graph MCP 多端配置指南

## 概述
Memory Knowledge Graph是一个基于HTTP的MCP服务器，支持在多个平台上使用。本指南将详细说明如何在不同平台上配置和使用。

## 平台配置

### 1. Windows端 (Kiro/Cursor等)

**配置文件位置**: `.kiro/settings/mcp.json` 或 `~/.cursor/mcp.json`

**配置内容**:
```json
{
  "mcpServers": {
    "memory-knowledge-graph": {
      "command": "uvx",
      "args": ["mcp-server-http", "--url", "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp"],
      "env": {
        "FASTMCP_LOG_LEVEL": "INFO"
      },
      "disabled": false,
      "autoApprove": ["search_memory", "get_related_memories"]
    }
  }
}
```

**使用步骤**:
1. 确保已安装 `uv` 和 `uvx`
2. 将配置复制到对应文件
3. 重启IDE或重新连接MCP服务器

### 2. 安卓端

**配置文件**: `android-config.json`

**关键配置**:
- 使用HTTP直接连接
- 设置合适的超时时间
- 启用缓存以提高性能

**集成方式**:
```javascript
// 安卓应用中的集成示例
const mcpClient = new MCPClient({
  baseUrl: 'https://cuisine-miss-regardless-walls.trycloudflare.com/mcp',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});
```

### 3. Web端

**配置文件**: `web-config.json`

**关键特性**:
- 使用Fetch API进行HTTP请求
- 处理CORS问题
- 支持WebSocket连接(如果服务器支持)

**集成示例**:
```javascript
// Web端集成示例
const memoryKG = {
  baseUrl: 'https://cuisine-miss-regardless-walls.trycloudflare.com/mcp',
  
  async addMemory(content, context, tags) {
    const response = await fetch(`${this.baseUrl}/tools/add_memory`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ content, context, tags })
    });
    return response.json();
  },
  
  async searchMemory(query, limit = 10) {
    const response = await fetch(`${this.baseUrl}/tools/search_memory`, {
      method: 'POST', 
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query, limit })
    });
    return response.json();
  }
};
```

## 工具说明

### 核心工具
1. **add_memory**: 添加新记忆到知识图谱
2. **search_memory**: 搜索现有记忆
3. **get_related_memories**: 获取相关记忆
4. **update_memory**: 更新现有记忆
5. **delete_memory**: 删除记忆

### 使用示例

#### 添加记忆
```json
{
  "tool": "add_memory",
  "parameters": {
    "content": "学习了React Hooks的使用方法",
    "context": "前端开发学习",
    "tags": ["React", "Hooks", "前端"]
  }
}
```

#### 搜索记忆
```json
{
  "tool": "search_memory", 
  "parameters": {
    "query": "React开发",
    "limit": 5,
    "similarity_threshold": 0.8
  }
}
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 增加超时时间设置
   - 确认服务器URL正确

2. **CORS错误 (Web端)**
   - 确保服务器支持CORS
   - 检查请求头设置
   - 考虑使用代理服务器

3. **认证失败**
   - 当前服务器不需要认证
   - 如果需要，添加API密钥到headers

### 调试技巧

1. **启用日志**:
   ```json
   "env": {
     "FASTMCP_LOG_LEVEL": "DEBUG"
   }
   ```

2. **测试连接**:
   ```bash
   curl -X POST https://cuisine-miss-regardless-walls.trycloudflare.com/mcp/tools/search_memory \
     -H "Content-Type: application/json" \
     -d '{"query": "test", "limit": 1}'
   ```

## 注意事项

1. **服务器稳定性**: CloudFlare隧道URL可能会变化，需要定期更新
2. **性能优化**: 合理设置缓存和超时时间
3. **安全考虑**: 在生产环境中考虑添加认证机制
4. **版本兼容**: 确保各端使用兼容的MCP协议版本

## 更新配置

当服务器URL或配置发生变化时：
1. 更新对应平台的配置文件
2. 重启应用或重新连接MCP服务器
3. 测试功能是否正常工作
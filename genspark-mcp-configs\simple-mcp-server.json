{"name": "Simple MCP Server", "description": "轻量级 MCP 服务器，提供记忆、笔记和基础工具功能", "type": "StreamableHttp", "url": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp", "headers": {"Content-Type": "application/json"}, "capabilities": {"tools": true, "resources": false, "prompts": false}, "tools": [{"name": "echo", "description": "回显输入的文本，用于测试连接", "parameters": {"text": {"type": "string", "description": "要回显的文本", "required": true}}}, {"name": "add", "description": "计算两个数字的和", "parameters": {"a": {"type": "number", "description": "第一个数字", "required": true}, "b": {"type": "number", "description": "第二个数字", "required": true}}}, {"name": "remember", "description": "存储重要信息到记忆系统", "parameters": {"content": {"type": "string", "description": "要记住的内容", "required": true}, "tags": {"type": "array", "description": "分类标签（可选）", "items": {"type": "string"}, "required": false}}}, {"name": "recall", "description": "搜索已存储的记忆", "parameters": {"query": {"type": "string", "description": "搜索关键词", "required": true}, "limit": {"type": "number", "description": "最大返回数量", "default": 5, "required": false}}}, {"name": "add_note", "description": "添加结构化笔记", "parameters": {"title": {"type": "string", "description": "笔记标题", "required": true}, "content": {"type": "string", "description": "笔记内容", "required": true}}}, {"name": "list_notes", "description": "列出最近的笔记", "parameters": {"limit": {"type": "number", "description": "最大返回数量", "default": 10, "required": false}}}], "examples": [{"description": "测试连接", "tool": "echo", "input": {"text": "Hello MC<PERSON>!"}}, {"description": "数学计算", "tool": "add", "input": {"a": 15, "b": 25}}, {"description": "存储学习记录", "tool": "remember", "input": {"content": "今天学习了 MCP 协议的配置方法", "tags": ["学习", "MCP", "技术"]}}, {"description": "搜索记忆", "tool": "recall", "input": {"query": "MCP", "limit": 3}}]}
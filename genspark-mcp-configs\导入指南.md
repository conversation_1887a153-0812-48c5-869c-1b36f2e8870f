# GenSpark MCP 配置导入指南

## 📋 可用的 MCP 服务器配置

### 1. Simple MCP Server（推荐新手）
**配置文件**: 提供多种格式
- `simple-mcp-server-standard.json` - 标准 MCP 格式
- `simple-mcp-server-basic.json` - 简化格式
- `simple-mcp-server-genspark.json` - GenSpark 特定格式
- `simple-mcp-server-array.json` - 数组格式

**服务器 URL**: `https://oh-proceedings-scene-surveys.trycloudflare.com/mcp`
**状态**: ✅ 运行中，所有工具测试通过

**特点**:
- 轻量级，易于使用
- 6个实用工具 (echo, add, remember, recall, add_note, list_notes)
- 稳定可靠
- 适合日常使用

### 2. Memory Knowledge Graph（高级用户）
**配置文件**: `memory-knowledge-graph.json`
**服务器 URL**: `https://cuisine-miss-regardless-walls.trycloudflare.com/mcp`
**状态**: ⚠️ 需要检查

**特点**:
- 知识图谱功能
- 语义搜索
- 复杂关系映射
- 适合专业用途

## 🔧 导入方法

### 方法一：手动配置（推荐）

#### Simple MCP Server
1. 在 GenSpark 中点击"添加 MCP 服务器"
2. 填写以下信息：
   ```
   服务器名称: Simple MCP Server
   服务器类型: StreamableHttp
   服务器 URL: https://oh-proceedings-scene-surveys.trycloudflare.com/mcp
   描述: 轻量级 MCP 服务器，提供记忆、笔记和基础工具功能
   请求头: {"Content-Type": "application/json"}
   ```

#### Memory Knowledge Graph
1. 在 GenSpark 中点击"添加 MCP 服务器"
2. 填写以下信息：
   ```
   服务器名称: Memory Knowledge Graph
   服务器类型: StreamableHttp
   服务器 URL: https://cuisine-miss-regardless-walls.trycloudflare.com/mcp
   描述: 高级知识图谱记忆系统，支持复杂的知识存储和检索
   请求头: {"Content-Type": "application/json"}
   ```

### 方法二：配置文件导入（推荐）

如果 GenSpark 支持配置文件导入，我们提供了多种格式：

1. **选择合适的配置文件**:
   - `simple-mcp-server-standard.json` - 标准 MCP 格式（推荐）
   - `simple-mcp-server-basic.json` - 简化格式
   - `simple-mcp-server-genspark.json` - GenSpark 特定格式
   - `simple-mcp-server-array.json` - 数组格式
   - `memory-knowledge-graph.json` - 知识图谱配置

2. **导入步骤**:
   - 在 GenSpark 设置中找到"导入配置"、"批量添加"或"从文件导入"
   - 选择对应的 JSON 配置文件（建议先尝试 standard 格式）
   - 如果导入失败，尝试其他格式的配置文件
   - 确认导入并测试连接

3. **格式说明**:
   - **Standard**: 符合 MCP 官方规范
   - **Basic**: 最简化的配置信息
   - **GenSpark**: 针对 GenSpark 优化的格式
   - **Array**: 数组形式，支持批量导入

### 方法三：批量配置脚本

如果需要批量配置，可以使用以下 PowerShell 脚本：

```powershell
# 测试 Simple MCP Server
$headers = @{'Content-Type' = 'application/json'}
$body = '{"jsonrpc": "2.0", "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "GenSpark", "version": "1.0.0"}}, "id": 1}'

Write-Host "测试 Simple MCP Server..."
try {
    $response = Invoke-WebRequest -Uri "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp" -Method POST -Headers $headers -Body $body
    Write-Host "✅ Simple MCP Server 可用" -ForegroundColor Green
} catch {
    Write-Host "❌ Simple MCP Server 不可用" -ForegroundColor Red
}

Write-Host "测试 Memory Knowledge Graph..."
try {
    $response = Invoke-WebRequest -Uri "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp" -Method POST -Headers $headers -Body $body
    Write-Host "✅ Memory Knowledge Graph 可用" -ForegroundColor Green
} catch {
    Write-Host "❌ Memory Knowledge Graph 不可用" -ForegroundColor Red
}
```

## 🎯 推荐配置

### 新手用户
**推荐**: Simple MCP Server
**原因**:
- 功能简单易懂
- 稳定可靠
- 学习成本低
- 满足日常需求

### 高级用户
**推荐**: 两个都配置
**原因**:
- Simple MCP Server 用于日常任务
- Memory Knowledge Graph 用于复杂知识管理
- 功能互补

## 🔍 验证配置

配置完成后，可以通过以下方式验证：

### Simple MCP Server 验证
```
用户: "请回显：测试连接"
AI: 使用 echo 工具...
预期结果: Echo: 测试连接
```

### Memory Knowledge Graph 验证
```
用户: "添加一条记忆：今天配置了 MCP 服务器"
AI: 使用 add_memory 工具...
预期结果: 记忆添加成功
```

## 🛠️ 故障排除

### 常见问题

1. **连接失败**
   - 检查 URL 是否正确
   - 确认服务器是否运行
   - 验证网络连接

2. **工具不可用**
   - 重新初始化连接
   - 检查服务器日志
   - 尝试重启服务器

3. **响应超时**
   - 增加超时设置
   - 检查服务器负载
   - 尝试简单的工具调用

### 服务器状态检查

使用以下命令检查服务器状态：

```bash
# 检查 Simple MCP Server
curl -X GET https://oh-proceedings-scene-surveys.trycloudflare.com/health

# 检查 Memory Knowledge Graph
curl -X GET https://cuisine-miss-regardless-walls.trycloudflare.com/health
```

## 📞 技术支持

如果遇到问题：

1. **查看日志**: 检查服务器控制台输出
2. **测试连接**: 使用 curl 或 PowerShell 测试
3. **重启服务**: 重启对应的服务器
4. **检查配置**: 确认所有参数正确

## 🎉 开始使用

配置完成后，您就可以开始使用强大的 MCP 功能了！

**第一次使用建议**:
1. 先测试 echo 工具确认连接
2. 尝试 remember 功能存储信息
3. 使用 recall 搜索之前的记忆
4. 探索其他高级功能

祝您使用愉快！🚀

# Simple MCP Server 测试脚本
# 用于验证 Simple MCP Server 的连接和功能

Write-Host "🚀 开始测试 Simple MCP Server..." -ForegroundColor Cyan
Write-Host "服务器 URL: https://oh-proceedings-scene-surveys.trycloudflare.com/mcp" -ForegroundColor Yellow
Write-Host ""

$headers = @{'Content-Type' = 'application/json'}
$baseUrl = "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp"

# 测试 1: 初始化连接
Write-Host "📡 测试 1: 初始化连接..." -ForegroundColor Green
$initBody = '{"jsonrpc": "2.0", "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "GenSpark", "version": "1.0.0"}}, "id": 1}'

try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method POST -Headers $headers -Body $initBody
    $result = $response.Content | ConvertFrom-Json
    if ($result.result.serverInfo.name -eq "simple-mcp-server") {
        Write-Host "✅ 初始化成功" -ForegroundColor Green
        Write-Host "   服务器名称: $($result.result.serverInfo.name)" -ForegroundColor Gray
        Write-Host "   协议版本: $($result.result.protocolVersion)" -ForegroundColor Gray
    } else {
        Write-Host "❌ 初始化失败：响应格式不正确" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 初始化失败：$($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 测试 2: 获取工具列表
Write-Host "🔧 测试 2: 获取工具列表..." -ForegroundColor Green
$toolsBody = '{"jsonrpc": "2.0", "method": "tools/list", "params": {}, "id": 2}'

try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method POST -Headers $headers -Body $toolsBody
    $result = $response.Content | ConvertFrom-Json
    $tools = $result.result.tools
    
    Write-Host "✅ 工具列表获取成功" -ForegroundColor Green
    Write-Host "   可用工具数量: $($tools.Count)" -ForegroundColor Gray
    
    foreach ($tool in $tools) {
        Write-Host "   - $($tool.name): $($tool.description)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 工具列表获取失败：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试 3: Echo 工具
Write-Host "🔊 测试 3: Echo 工具..." -ForegroundColor Green
$echoBody = '{"jsonrpc": "2.0", "method": "tools/call", "params": {"name": "echo", "arguments": {"text": "Hello MCP!"}}, "id": 3}'

try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method POST -Headers $headers -Body $echoBody
    $result = $response.Content | ConvertFrom-Json
    $content = $result.result.content[0].text
    
    if ($content -like "*Hello MCP!*") {
        Write-Host "✅ Echo 工具测试成功" -ForegroundColor Green
        Write-Host "   响应: $content" -ForegroundColor Gray
    } else {
        Write-Host "❌ Echo 工具测试失败：响应内容不正确" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Echo 工具测试失败：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试 4: Add 工具
Write-Host "➕ 测试 4: Add 工具..." -ForegroundColor Green
$addBody = '{"jsonrpc": "2.0", "method": "tools/call", "params": {"name": "add", "arguments": {"a": 15, "b": 25}}, "id": 4}'

try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method POST -Headers $headers -Body $addBody
    $result = $response.Content | ConvertFrom-Json
    $content = $result.result.content[0].text
    
    if ($content -like "*40*") {
        Write-Host "✅ Add 工具测试成功" -ForegroundColor Green
        Write-Host "   响应: $content" -ForegroundColor Gray
    } else {
        Write-Host "❌ Add 工具测试失败：计算结果不正确" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Add 工具测试失败：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试 5: Remember 工具
Write-Host "🧠 测试 5: Remember 工具..." -ForegroundColor Green
$rememberBody = '{"jsonrpc": "2.0", "method": "tools/call", "params": {"name": "remember", "arguments": {"content": "测试记忆：今天配置了 MCP 服务器", "tags": ["测试", "MCP", "配置"]}}, "id": 5}'

try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method POST -Headers $headers -Body $rememberBody
    $result = $response.Content | ConvertFrom-Json
    $content = $result.result.content[0].text
    
    if ($content -like "*记忆已保存*") {
        Write-Host "✅ Remember 工具测试成功" -ForegroundColor Green
        Write-Host "   响应: $($content.Substring(0, [Math]::Min(50, $content.Length)))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ Remember 工具测试失败：响应格式不正确" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Remember 工具测试失败：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试 6: Recall 工具
Write-Host "🔍 测试 6: Recall 工具..." -ForegroundColor Green
$recallBody = '{"jsonrpc": "2.0", "method": "tools/call", "params": {"name": "recall", "arguments": {"query": "MCP", "limit": 3}}, "id": 6}'

try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method POST -Headers $headers -Body $recallBody
    $result = $response.Content | ConvertFrom-Json
    $content = $result.result.content[0].text
    
    if ($content -like "*找到*记忆*" -or $content -like "*没有找到*") {
        Write-Host "✅ Recall 工具测试成功" -ForegroundColor Green
        Write-Host "   响应: $($content.Substring(0, [Math]::Min(50, $content.Length)))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ Recall 工具测试失败：响应格式不正确" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Recall 工具测试失败：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试 7: GET 端点（应该返回 405）
Write-Host "🚫 测试 7: GET 端点（应该返回 405）..." -ForegroundColor Green

try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method GET
    Write-Host "❌ GET 端点测试失败：应该返回 405 错误" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 405) {
        Write-Host "✅ GET 端点测试成功：正确返回 405 Method Not Allowed" -ForegroundColor Green
    } else {
        Write-Host "❌ GET 端点测试失败：返回了错误的状态码" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎉 Simple MCP Server 测试完成！" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 GenSpark 配置信息：" -ForegroundColor Yellow
Write-Host "   服务器名称: Simple MCP Server" -ForegroundColor Gray
Write-Host "   服务器类型: StreamableHttp" -ForegroundColor Gray
Write-Host "   服务器 URL: https://oh-proceedings-scene-surveys.trycloudflare.com/mcp" -ForegroundColor Gray
Write-Host "   请求头: {`"Content-Type`": `"application/json`"}" -ForegroundColor Gray
Write-Host ""

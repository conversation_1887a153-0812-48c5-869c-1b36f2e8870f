# 解决 405 错误的完整指南

## 🔍 问题分析

您遇到的错误 "unexpected status code: 405" 表明通义灵码在尝试建立 MCP 连接时遇到了问题。这通常有以下几个原因：

1. **连接方式不匹配**：通义灵码可能期望 SSE 或 stdio 连接，而不是纯 HTTP
2. **协议版本不兼容**：MCP 协议版本可能不匹配
3. **配置格式问题**：配置文件格式可能需要调整

## 🔧 解决方案

### 方案一：使用 SSE 连接（推荐）

**配置文件**: `.mcp-sse.json`
```json
{
  "mcpServers": {
    "simple-mcp-server": {
      "type": "sse",
      "url": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

### 方案二：使用 stdio 连接

**配置文件**: `.mcp-stdio.json`
```json
{
  "mcpServers": {
    "simple-mcp-server": {
      "command": "node",
      "args": [
        "D:\\Codefield\\simple-mcp-server\\mcp-client-server.js",
        "--stdio"
      ],
      "env": {}
    }
  }
}
```

### 方案三：修改现有配置

如果您想继续使用 HTTP 方式，请尝试以下配置：

```json
{
  "mcpServers": {
    "simple-mcp-server": {
      "type": "http",
      "url": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp",
      "headers": {
        "Content-Type": "application/json",
        "Accept": "application/json"
      }
    }
  }
}
```

## 📋 逐步解决步骤

### 步骤 1：确认服务器运行状态

```powershell
# 测试服务器是否正常运行
$headers = @{'Content-Type' = 'application/json'}
$body = '{"jsonrpc": "2.0", "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "Test", "version": "1.0.0"}}, "id": 1}'
Invoke-WebRequest -Uri "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp" -Method POST -Headers $headers -Body $body
```

### 步骤 2：尝试不同的配置

1. **首先尝试 SSE 配置**：
   - 复制 `.mcp-sse.json` 内容到 `.mcp.json`
   - 重启通义灵码

2. **如果 SSE 不工作，尝试 stdio**：
   - 复制 `.mcp-stdio.json` 内容到 `.mcp.json`
   - 确保路径正确
   - 重启通义灵码

3. **如果都不工作，检查通义灵码文档**：
   - 查看通义灵码的 MCP 配置文档
   - 确认支持的连接类型

### 步骤 3：调试连接

如果仍然有问题，请检查：

1. **网络连接**：
   ```powershell
   Test-NetConnection oh-proceedings-scene-surveys.trycloudflare.com -Port 443
   ```

2. **服务器日志**：
   - 查看 Simple MCP Server 的控制台输出
   - 检查是否有连接尝试

3. **通义灵码日志**：
   - 查看通义灵码的错误日志
   - 寻找更详细的错误信息

## 🛠️ 替代方案

### 方案 A：使用本地服务器

如果网络连接有问题，可以使用本地服务器：

```json
{
  "mcpServers": {
    "simple-mcp-server": {
      "type": "http",
      "url": "http://localhost:3001/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

### 方案 B：使用不同的端口

尝试使用不同的服务器端口：

```json
{
  "mcpServers": {
    "simple-mcp-server": {
      "type": "http",
      "url": "https://oh-proceedings-scene-surveys.trycloudflare.com:443/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

## 🔍 常见问题排查

### Q1: 405 错误持续出现
**A**: 尝试使用 SSE 连接方式，这是许多 MCP 客户端的首选方式。

### Q2: 连接超时
**A**: 检查网络连接和防火墙设置，确保可以访问 Cloudflare Tunnel。

### Q3: 工具不显示
**A**: 确认服务器初始化成功，检查 `tools/list` 方法是否正常响应。

### Q4: 配置文件不生效
**A**: 确认文件名为 `.mcp.json` 且位于正确位置，重启通义灵码。

## 📞 获取帮助

如果问题仍然存在：

1. **检查通义灵码版本**：确保使用最新版本
2. **查看官方文档**：查找 MCP 配置的最新说明
3. **尝试其他 MCP 客户端**：如 Claude Desktop，验证服务器是否正常
4. **联系技术支持**：提供详细的错误日志

## 🎯 推荐配置

基于常见的 MCP 客户端行为，我推荐按以下顺序尝试：

1. **SSE 配置** (最兼容)
2. **stdio 配置** (最标准)
3. **HTTP 配置** (最简单)

选择最适合您环境的配置方式。

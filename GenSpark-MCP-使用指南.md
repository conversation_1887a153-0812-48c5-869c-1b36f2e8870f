# GenSpark MCP 服务器使用指南

🎉 恭喜！您已成功配置了 MCP 服务器。本指南将帮助您充分利用这个强大的工具系统。

## 📋 当前可用工具

### 🔧 基础工具

#### 1. echo - 文本回显
**用途**: 测试连接，回显任何文本
**使用场景**: 
- 验证 MCP 连接是否正常
- 简单的文本处理

**示例对话**:
```
用户: "请回显：Hello MCP!"
AI: 使用 echo 工具...
结果: Echo: Hello MCP!
```

#### 2. add - 数字加法
**用途**: 计算两个数字的和
**使用场景**:
- 快速数学计算
- 验证计算结果

**示例对话**:
```
用户: "计算 25 + 37"
AI: 使用 add 工具...
结果: 25 + 37 = 62
```

### 🧠 记忆系统

#### 3. remember - 存储记忆
**用途**: 保存重要信息，支持标签分类
**使用场景**:
- 记录学习要点
- 保存项目信息
- 存储重要决策

**示例对话**:
```
用户: "请记住：明天下午3点有重要会议，讨论Q4规划"
AI: 使用 remember 工具...
结果: ✅ 记忆已保存 (ID: 1234567890)
内容: 明天下午3点有重要会议，讨论Q4规划
标签: 会议, 规划
```

#### 4. recall - 搜索记忆
**用途**: 根据关键词搜索已存储的记忆
**使用场景**:
- 查找之前的对话内容
- 回顾学习笔记
- 检索项目信息

**示例对话**:
```
用户: "帮我找找关于会议的记忆"
AI: 使用 recall 工具...
结果: 🔍 找到 2 条相关记忆:
📝 ID: 1234567890
内容: 明天下午3点有重要会议，讨论Q4规划
标签: 会议, 规划
时间: 2025-07-16T13:28:40.857Z
...
```

### 📝 笔记系统

#### 5. add_note - 添加笔记
**用途**: 快速创建结构化笔记
**使用场景**:
- 记录待办事项
- 保存学习笔记
- 创建提醒事项

**示例对话**:
```
用户: "帮我添加一个笔记：周末学习计划"
AI: 使用 add_note 工具...
结果: 📝 笔记已添加 (ID: 9876543210)
标题: 周末学习计划
内容: 复习 Python 基础，完成项目文档
```

#### 6. list_notes - 列出笔记
**用途**: 查看最近的笔记列表
**使用场景**:
- 回顾最近的笔记
- 检查待办事项
- 浏览学习记录

**示例对话**:
```
用户: "显示我最近的笔记"
AI: 使用 list_notes 工具...
结果: 📝 最近的 3 条笔记:
📝 ID: 9876543210
标题: 周末学习计划
内容: 复习 Python 基础，完成项目文档
时间: 2025-07-16T13:30:15.123Z
...
```

## 💡 实用使用技巧

### 1. 学习助手
```
用户: "我刚学了 MCP 协议，帮我记录一下要点"
AI: 我来帮您记录学习要点...
[使用 remember 工具存储]
```

### 2. 项目管理
```
用户: "记录：项目 Alpha 需要在月底前完成数据库设计"
AI: 我来为您记录这个项目信息...
[使用 remember 工具，标签：项目, Alpha, 数据库]
```

### 3. 知识检索
```
用户: "帮我找找之前关于数据库的所有记录"
AI: 我来搜索相关记忆...
[使用 recall 工具搜索 "数据库"]
```

### 4. 快速笔记
```
用户: "添加笔记：明天要买的东西清单"
AI: 我来为您创建笔记...
[使用 add_note 工具]
```

## 🎯 最佳实践

### 记忆存储建议
- **使用描述性标签**: 便于后续搜索
- **内容要具体**: 包含时间、地点、人物等关键信息
- **定期回顾**: 使用 recall 功能复习重要内容

### 笔记管理建议
- **标题要简洁明了**: 便于快速识别
- **内容结构化**: 使用列表、分点等格式
- **及时整理**: 定期使用 list_notes 查看和整理

### 搜索技巧
- **使用关键词**: 选择最具代表性的词汇
- **组合搜索**: 尝试不同的关键词组合
- **标签搜索**: 利用标签进行分类搜索

## 🔄 工作流程示例

### 日常学习流程
1. **学习新知识** → 使用 `remember` 记录要点
2. **复习时间** → 使用 `recall` 搜索相关内容
3. **制定计划** → 使用 `add_note` 创建学习计划
4. **进度检查** → 使用 `list_notes` 查看计划执行情况

### 项目管理流程
1. **项目启动** → 使用 `remember` 记录项目目标和要求
2. **任务分解** → 使用 `add_note` 创建任务清单
3. **进度跟踪** → 使用 `recall` 查找项目相关信息
4. **总结回顾** → 使用 `list_notes` 查看项目笔记

## 🆘 常见问题

**Q: 记忆和笔记有什么区别？**
A: 记忆适合存储零散的重要信息，支持标签分类；笔记适合结构化的内容，有明确的标题和内容。

**Q: 如何更好地搜索记忆？**
A: 使用具体的关键词，尝试不同的搜索词组合，利用标签进行分类搜索。

**Q: 数据会丢失吗？**
A: 当前版本重启服务器后数据会丢失，建议重要信息做好备份。

## 🎉 开始使用

现在您可以开始与 AI 对话，让它帮您使用这些强大的工具了！试试说：

- "帮我记住今天学到的重要内容"
- "搜索一下关于项目的记忆"
- "添加一个明天的待办事项笔记"
- "显示我最近的笔记"

享受您的 MCP 之旅！🚀

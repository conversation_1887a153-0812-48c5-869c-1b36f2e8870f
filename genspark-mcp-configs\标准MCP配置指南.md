# 标准 MCP 配置指南

## 🎯 问题解决

您遇到的问题是配置文件格式不符合标准 MCP 格式。通义灵码和其他 MCP 客户端使用标准的 `.mcp.json` 配置格式。

## 📋 标准 MCP 配置格式

### 基本格式
```json
{
  "mcpServers": {
    "server-name": {
      "type": "http",
      "url": "https://your-server-url.com/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

## 🔧 正确的配置文件

### 1. Simple MCP Server 配置

**文件名**: `simple-mcp-server-standard.json` 或 `.mcp.json`

```json
{
  "mcpServers": {
    "simple-mcp-server": {
      "type": "http",
      "url": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

### 2. 完整配置（两个服务器）

**文件名**: `.mcp.json`

```json
{
  "mcpServers": {
    "simple-mcp-server": {
      "type": "http",
      "url": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    },
    "memory-knowledge-graph": {
      "type": "http", 
      "url": "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

## 📁 配置文件使用方法

### 方法一：项目级配置
1. 将 `.mcp.json` 文件放在项目根目录
2. 通义灵码会自动检测并加载配置
3. 团队成员可以共享相同配置

### 方法二：用户级配置
1. 将配置文件放在用户配置目录
2. 所有项目都可以使用这些服务器
3. 个人专用配置

### 方法三：通过命令行添加（如果支持）
```bash
# 添加 Simple MCP Server
claude mcp add --transport http simple-mcp-server https://oh-proceedings-scene-surveys.trycloudflare.com/mcp --header "Content-Type: application/json"

# 添加 Memory Knowledge Graph
claude mcp add --transport http memory-knowledge-graph https://cuisine-miss-regardless-walls.trycloudflare.com/mcp --header "Content-Type: application/json"
```

## 🔍 验证配置

### 1. 检查配置文件语法
确保 JSON 格式正确，没有语法错误。

### 2. 测试服务器连接
```bash
# 测试 Simple MCP Server
curl -X POST https://oh-proceedings-scene-surveys.trycloudflare.com/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "Test", "version": "1.0.0"}}, "id": 1}'
```

### 3. 在通义灵码中验证
1. 重启通义灵码
2. 检查 MCP 服务器是否出现在列表中
3. 尝试调用工具测试功能

## 🛠️ 支持的服务器类型

### HTTP 服务器（推荐）
```json
{
  "type": "http",
  "url": "https://example.com/mcp",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer your-token"
  }
}
```

### SSE 服务器
```json
{
  "type": "sse",
  "url": "https://example.com/sse",
  "headers": {
    "Content-Type": "application/json"
  }
}
```

### Stdio 服务器
```json
{
  "command": "/path/to/server",
  "args": ["--option", "value"],
  "env": {
    "API_KEY": "your-key"
  }
}
```

## 📝 配置文件位置

### 通义灵码配置位置
- **项目级**: 项目根目录的 `.mcp.json`
- **用户级**: 用户配置目录（具体位置取决于系统）

### Claude Desktop 配置位置
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

## 🔧 故障排除

### 1. 配置文件不生效
- 检查文件名是否正确（`.mcp.json`）
- 确认 JSON 语法正确
- 重启 MCP 客户端

### 2. 服务器连接失败
- 验证 URL 是否可访问
- 检查网络连接
- 确认服务器正在运行

### 3. 工具不显示
- 确认服务器初始化成功
- 检查工具列表 API 响应
- 查看客户端日志

## 📋 推荐配置

### 新手用户
使用 Simple MCP Server 配置：
```json
{
  "mcpServers": {
    "simple-mcp-server": {
      "type": "http",
      "url": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

### 高级用户
使用完整配置，包含两个服务器。

## 🎉 开始使用

1. **复制正确的配置文件**
2. **放置在正确位置**
3. **重启通义灵码**
4. **验证服务器连接**
5. **开始使用 MCP 工具**

现在您应该能够在通义灵码中看到 MCP 服务器了！🚀

#!/usr/bin/env node

/**
 * MCP Client Compatible Server
 * 专为通义灵码等 MCP 客户端优化的服务器
 * 支持 stdio 和 SSE 两种连接方式
 */

const express = require('express');
const cors = require('cors');

// 简单的内存存储
const memories = [];
const notes = [];

// 工具定义
const tools = [
  {
    name: 'echo',
    description: 'Echo back the input text',
    inputSchema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: 'Text to echo back'
        }
      },
      required: ['text']
    }
  },
  {
    name: 'add',
    description: 'Add two numbers',
    inputSchema: {
      type: 'object',
      properties: {
        a: {
          type: 'number',
          description: 'First number'
        },
        b: {
          type: 'number',
          description: 'Second number'
        }
      },
      required: ['a', 'b']
    }
  },
  {
    name: 'remember',
    description: 'Store a memory or important information',
    inputSchema: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: 'The content to remember'
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: 'Optional tags for categorization'
        }
      },
      required: ['content']
    }
  },
  {
    name: 'recall',
    description: 'Search and retrieve stored memories',
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search query to find relevant memories'
        },
        limit: {
          type: 'number',
          description: 'Maximum number of results to return',
          default: 5
        }
      },
      required: ['query']
    }
  }
];

// 工具处理函数
function handleToolCall(name, args) {
  switch (name) {
    case 'echo':
      return {
        content: [
          {
            type: 'text',
            text: `Echo: ${args.text}`
          }
        ]
      };

    case 'add':
      const result = args.a + args.b;
      return {
        content: [
          {
            type: 'text',
            text: `${args.a} + ${args.b} = ${result}`
          }
        ]
      };

    case 'remember':
      const memory = {
        id: Date.now(),
        content: args.content,
        tags: args.tags || [],
        timestamp: new Date().toISOString()
      };
      memories.push(memory);
      return {
        content: [
          {
            type: 'text',
            text: `✅ 记忆已保存 (ID: ${memory.id})\n内容: ${memory.content}\n标签: ${memory.tags.join(', ') || '无'}`
          }
        ]
      };

    case 'recall':
      const query = args.query.toLowerCase();
      const limit = args.limit || 5;
      const matchingMemories = memories
        .filter(memory => 
          memory.content.toLowerCase().includes(query) ||
          memory.tags.some(tag => tag.toLowerCase().includes(query))
        )
        .slice(0, limit);

      if (matchingMemories.length === 0) {
        return {
          content: [
            {
              type: 'text',
              text: `❌ 没有找到与 "${args.query}" 相关的记忆`
            }
          ]
        };
      }

      const resultText = matchingMemories
        .map(memory => `📝 ID: ${memory.id}\n内容: ${memory.content}\n标签: ${memory.tags.join(', ') || '无'}\n时间: ${memory.timestamp}`)
        .join('\n\n');

      return {
        content: [
          {
            type: 'text',
            text: `🔍 找到 ${matchingMemories.length} 条相关记忆:\n\n${resultText}`
          }
        ]
      };

    default:
      throw new Error(`Unknown tool: ${name}`);
  }
}

// MCP 消息处理
function handleMCPMessage(message) {
  const { method, params, id } = message;

  switch (method) {
    case 'initialize':
      return {
        jsonrpc: '2.0',
        id,
        result: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {}
          },
          serverInfo: {
            name: 'simple-mcp-server',
            version: '1.0.0'
          }
        }
      };

    case 'tools/list':
      return {
        jsonrpc: '2.0',
        id,
        result: {
          tools
        }
      };

    case 'tools/call':
      try {
        const result = handleToolCall(params.name, params.arguments);
        return {
          jsonrpc: '2.0',
          id,
          result
        };
      } catch (error) {
        return {
          jsonrpc: '2.0',
          id,
          error: {
            code: -32000,
            message: error.message
          }
        };
      }

    default:
      return {
        jsonrpc: '2.0',
        id,
        error: {
          code: -32601,
          message: `Method not found: ${method}`
        }
      };
  }
}

// 检查是否在 stdio 模式下运行
if (process.argv.includes('--stdio')) {
  // Stdio 模式 - 用于通义灵码等客户端
  console.error('🚀 MCP Server running in stdio mode');
  
  let buffer = '';
  
  process.stdin.on('data', (chunk) => {
    buffer += chunk.toString();
    
    // 处理多个消息
    const lines = buffer.split('\n');
    buffer = lines.pop(); // 保留不完整的行
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const message = JSON.parse(line);
          const response = handleMCPMessage(message);
          console.log(JSON.stringify(response));
        } catch (error) {
          console.error('Error processing message:', error);
        }
      }
    }
  });
  
  process.stdin.on('end', () => {
    process.exit(0);
  });
  
} else {
  // HTTP/SSE 模式
  const app = express();
  const PORT = process.env.PORT || 3002;

  app.use(cors());
  app.use(express.json());

  // SSE 端点
  app.get('/mcp', (req, res) => {
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*'
    });

    res.write('event: message\n');
    res.write('data: {"jsonrpc": "2.0", "method": "notifications/initialized", "params": {}}\n\n');

    const keepAlive = setInterval(() => {
      res.write('event: ping\n');
      res.write('data: {}\n\n');
    }, 30000);

    req.on('close', () => {
      clearInterval(keepAlive);
    });
  });

  // HTTP POST 端点
  app.post('/mcp', (req, res) => {
    try {
      const response = handleMCPMessage(req.body);
      res.json(response);
    } catch (error) {
      res.status(500).json({
        jsonrpc: '2.0',
        id: req.body.id,
        error: {
          code: -32603,
          message: 'Internal error'
        }
      });
    }
  });

  app.listen(PORT, () => {
    console.log(`🚀 MCP Server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`🔧 MCP endpoint: http://localhost:${PORT}/mcp`);
  });
}

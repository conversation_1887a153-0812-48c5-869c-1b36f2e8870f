// 配置向导 JavaScript

let currentStep = 1;
let selectedService = '';
let tunnelUrl = '';

// DOM 元素
const progressFill = document.getElementById('progressFill');
const steps = document.querySelectorAll('.step');
const serviceCards = document.querySelectorAll('.service-card');
const nextStep1 = document.getElementById('nextStep1');
const tunnelUrlInput = document.getElementById('tunnelUrl');
const tunnelHelp = document.getElementById('tunnelHelp');
const configContent = document.getElementById('configContent');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 服务选择卡片
    serviceCards.forEach(card => {
        card.addEventListener('click', function() {
            selectService(this.dataset.service);
        });
    });
    
    // 隧道 URL 输入
    tunnelUrlInput.addEventListener('input', function() {
        validateTunnelUrl();
    });
}

// 选择服务
function selectService(service) {
    selectedService = service;
    
    // 更新卡片样式
    serviceCards.forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-service="${service}"]`).classList.add('selected');
    
    // 启用下一步按钮
    nextStep1.disabled = false;
}

// 验证隧道 URL
function validateTunnelUrl() {
    const url = tunnelUrlInput.value.trim();
    const nextBtn = document.getElementById('nextStep2');
    
    if (url && isValidUrl(url)) {
        tunnelUrl = url;
        nextBtn.disabled = false;
    } else {
        nextBtn.disabled = true;
    }
}

// 检查 URL 有效性
function isValidUrl(string) {
    try {
        const url = new URL(string);
        return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (_) {
        return false;
    }
}

// 显示隧道工具帮助
function showTunnelHelp(tool) {
    const helpContent = {
        ngrok: `# 使用 Ngrok

1. 安装 Ngrok:
   下载: https://ngrok.com/download
   或使用 npm: npm install -g ngrok

2. 启动隧道:
   ngrok http 3000

3. 复制 HTTPS URL (类似 https://abc123.ngrok.io)`,

        localtunnel: `# 使用 Localtunnel

1. 安装 Localtunnel:
   npm install -g localtunnel

2. 启动隧道:
   lt --port 3000

3. 复制生成的 URL`,

        serveo: `# 使用 Serveo

1. 启动隧道 (无需安装):
   ssh -R 80:localhost:3000 serveo.net

2. 复制生成的 URL`
    };
    
    tunnelHelp.textContent = helpContent[tool];
    tunnelHelp.style.display = 'block';
}

// 下一步
function nextStep() {
    if (currentStep < 4) {
        currentStep++;
        updateStep();
        
        if (currentStep === 3) {
            generateConfig();
        }
    }
}

// 上一步
function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStep();
    }
}

// 更新步骤显示
function updateStep() {
    // 隐藏所有步骤
    steps.forEach(step => {
        step.classList.remove('active');
    });
    
    // 显示当前步骤
    document.getElementById(`step${currentStep}`).classList.add('active');
    
    // 更新进度条
    const progress = (currentStep / 4) * 100;
    progressFill.style.width = `${progress}%`;
}

// 生成配置
function generateConfig() {
    const configs = {
        coze: generateCozeConfig(),
        genspark: generateGenSparkConfig(),
        custom: generateCustomConfig()
    };
    
    configContent.innerHTML = configs[selectedService];
}

// 生成 Coze 配置
function generateCozeConfig() {
    const config = {
        name: "MCP Bridge for Coze",
        base_url: tunnelUrl,
        endpoints: {
            health: `${tunnelUrl}/health`,
            api_call: `${tunnelUrl}/api/call`
        },
        usage: {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body_template: {
                server: "notion",
                tool: "tool_name",
                args: {}
            }
        }
    };
    
    return `
        <div class="alert alert-info">
            <strong>Coze 配置信息</strong><br>
            请在 Coze 中添加以下 API 配置
        </div>
        
        <div class="form-group">
            <label>API 端点 URL:</label>
            <div class="config-output">
${tunnelUrl}/api/call
                <button class="copy-btn" onclick="copyToClipboard('${tunnelUrl}/api/call')">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label>请求方法:</label>
            <div class="config-output">POST</div>
        </div>
        
        <div class="form-group">
            <label>请求头:</label>
            <div class="config-output">Content-Type: application/json</div>
        </div>
        
        <div class="form-group">
            <label>请求体示例 (搜索 Notion 页面):</label>
            <div class="config-output">${JSON.stringify({
                server: "notion",
                tool: "search_pages",
                args: { query: "项目计划" }
            }, null, 2)}
                <button class="copy-btn" onclick="copyToClipboard('${JSON.stringify({
                    server: "notion",
                    tool: "search_pages",
                    args: { query: "项目计划" }
                }, null, 2)}')">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label>完整配置 JSON:</label>
            <div class="config-output">${JSON.stringify(config, null, 2)}
                <button class="copy-btn" onclick="copyToClipboard('${JSON.stringify(config, null, 2)}')">复制</button>
            </div>
        </div>
    `;
}

// 生成 GenSpark 配置
function generateGenSparkConfig() {
    const config = {
        name: "MCP Bridge for GenSpark",
        base_url: tunnelUrl,
        health_check: `${tunnelUrl}/health`,
        api_endpoint: `${tunnelUrl}/api/call`,
        method: "POST",
        content_type: "application/json"
    };
    
    return `
        <div class="alert alert-info">
            <strong>GenSpark 配置信息</strong><br>
            请在 GenSpark 中使用以下配置
        </div>
        
        <div class="form-group">
            <label>Base URL:</label>
            <div class="config-output">
${tunnelUrl}
                <button class="copy-btn" onclick="copyToClipboard('${tunnelUrl}')">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label>健康检查端点:</label>
            <div class="config-output">
${tunnelUrl}/health
                <button class="copy-btn" onclick="copyToClipboard('${tunnelUrl}/health')">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label>API 调用端点:</label>
            <div class="config-output">
${tunnelUrl}/api/call
                <button class="copy-btn" onclick="copyToClipboard('${tunnelUrl}/api/call')">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label>示例请求:</label>
            <div class="config-output">curl -X POST ${tunnelUrl}/api/call \\
  -H "Content-Type: application/json" \\
  -d '{"server": "memory", "tool": "search_memory", "args": {"query": "项目"}}'
                <button class="copy-btn" onclick="copyToClipboard('curl -X POST ${tunnelUrl}/api/call -H \"Content-Type: application/json\" -d \\'{\"server\": \"memory\", \"tool\": \"search_memory\", \"args\": {\"query\": \"项目\"}}\\\'')">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label>完整配置 JSON:</label>
            <div class="config-output">${JSON.stringify(config, null, 2)}
                <button class="copy-btn" onclick="copyToClipboard('${JSON.stringify(config, null, 2)}')">复制</button>
            </div>
        </div>
    `;
}

// 生成自定义配置
function generateCustomConfig() {
    return `
        <div class="alert alert-info">
            <strong>自定义服务配置</strong><br>
            通用 HTTP API 配置信息
        </div>
        
        <div class="form-group">
            <label>Base URL:</label>
            <div class="config-output">
${tunnelUrl}
                <button class="copy-btn" onclick="copyToClipboard('${tunnelUrl}')">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label>主要端点:</label>
            <div class="config-output">
健康检查: GET ${tunnelUrl}/health
服务器列表: GET ${tunnelUrl}/api/servers
工具列表: GET ${tunnelUrl}/api/servers/{server}/tools
工具调用: POST ${tunnelUrl}/api/call
            </div>
        </div>
        
        <div class="form-group">
            <label>通用调用格式:</label>
            <div class="config-output">{
  "server": "notion|memory",
  "tool": "tool_name",
  "args": {
    // 工具参数
  }
}
                <button class="copy-btn" onclick="copyToClipboard('{\\n  \"server\": \"notion|memory\",\\n  \"tool\": \"tool_name\",\\n  \"args\": {\\n    // 工具参数\\n  }\\n}')">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label>响应格式:</label>
            <div class="config-output">{
  "success": true,
  "server": "server_name",
  "tool": "tool_name",
  "result": {
    // 工具执行结果
  }
}
            </div>
        </div>
    `;
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // 显示复制成功提示
        const btn = event.target;
        const originalText = btn.textContent;
        btn.textContent = '已复制!';
        btn.style.background = '#28a745';
        
        setTimeout(() => {
            btn.textContent = originalText;
            btn.style.background = '#28a745';
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制');
    });
}

// 重新开始向导
function restartWizard() {
    currentStep = 1;
    selectedService = '';
    tunnelUrl = '';
    
    // 重置表单
    serviceCards.forEach(card => {
        card.classList.remove('selected');
    });
    tunnelUrlInput.value = '';
    tunnelHelp.style.display = 'none';
    nextStep1.disabled = true;
    
    updateStep();
}

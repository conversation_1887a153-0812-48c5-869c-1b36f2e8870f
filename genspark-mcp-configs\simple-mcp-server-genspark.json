{"servers": [{"id": "simple-mcp-server", "name": "Simple MCP Server", "description": "轻量级 MCP 服务器，提供记忆、笔记和基础工具功能", "serverType": "StreamableHttp", "endpoint": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp", "headers": {"Content-Type": "application/json"}, "enabled": true, "capabilities": ["tools"], "tools": [{"name": "echo", "description": "回显输入的文本，用于测试连接"}, {"name": "add", "description": "计算两个数字的和"}, {"name": "remember", "description": "存储重要信息到记忆系统"}, {"name": "recall", "description": "搜索已存储的记忆"}, {"name": "add_note", "description": "添加结构化笔记"}, {"name": "list_notes", "description": "列出最近的笔记"}]}]}
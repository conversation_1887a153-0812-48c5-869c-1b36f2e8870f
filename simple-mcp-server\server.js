import express from 'express';

// 简单的内存存储
const memories = [];
const notes = [];

// 简单的工具数据
const tools = [
  {
    name: 'echo',
    description: 'Echo back the input text',
    inputSchema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: 'Text to echo back'
        }
      },
      required: ['text']
    }
  },
  {
    name: 'add',
    description: 'Add two numbers',
    inputSchema: {
      type: 'object',
      properties: {
        a: {
          type: 'number',
          description: 'First number'
        },
        b: {
          type: 'number',
          description: 'Second number'
        }
      },
      required: ['a', 'b']
    }
  },
  {
    name: 'remember',
    description: 'Store a memory or important information',
    inputSchema: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: 'The content to remember'
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: 'Optional tags for categorization'
        }
      },
      required: ['content']
    }
  },
  {
    name: 'recall',
    description: 'Search and retrieve stored memories',
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search query to find relevant memories'
        },
        limit: {
          type: 'number',
          description: 'Maximum number of results to return',
          default: 5
        }
      },
      required: ['query']
    }
  },
  {
    name: 'add_note',
    description: 'Add a quick note',
    inputSchema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Note title'
        },
        content: {
          type: 'string',
          description: 'Note content'
        }
      },
      required: ['title', 'content']
    }
  },
  {
    name: 'list_notes',
    description: 'List all stored notes',
    inputSchema: {
      type: 'object',
      properties: {
        limit: {
          type: 'number',
          description: 'Maximum number of notes to return',
          default: 10
        }
      }
    }
  }
];

// 处理工具调用的函数
function handleToolCall(name, args) {
  switch (name) {
    case 'echo':
      return {
        content: [
          {
            type: 'text',
            text: `Echo: ${args.text}`
          }
        ]
      };

    case 'add':
      const result = args.a + args.b;
      return {
        content: [
          {
            type: 'text',
            text: `${args.a} + ${args.b} = ${result}`
          }
        ]
      };

    case 'remember':
      const memory = {
        id: Date.now(),
        content: args.content,
        tags: args.tags || [],
        timestamp: new Date().toISOString()
      };
      memories.push(memory);
      return {
        content: [
          {
            type: 'text',
            text: `✅ 记忆已保存 (ID: ${memory.id})\n内容: ${memory.content}\n标签: ${memory.tags.join(', ') || '无'}`
          }
        ]
      };

    case 'recall':
      const query = args.query.toLowerCase();
      const limit = args.limit || 5;
      const matchingMemories = memories
        .filter(memory =>
          memory.content.toLowerCase().includes(query) ||
          memory.tags.some(tag => tag.toLowerCase().includes(query))
        )
        .slice(0, limit);

      if (matchingMemories.length === 0) {
        return {
          content: [
            {
              type: 'text',
              text: `❌ 没有找到与 "${args.query}" 相关的记忆`
            }
          ]
        };
      }

      const resultText = matchingMemories
        .map(memory => `📝 ID: ${memory.id}\n内容: ${memory.content}\n标签: ${memory.tags.join(', ') || '无'}\n时间: ${memory.timestamp}`)
        .join('\n\n');

      return {
        content: [
          {
            type: 'text',
            text: `🔍 找到 ${matchingMemories.length} 条相关记忆:\n\n${resultText}`
          }
        ]
      };

    case 'add_note':
      const note = {
        id: Date.now(),
        title: args.title,
        content: args.content,
        timestamp: new Date().toISOString()
      };
      notes.push(note);
      return {
        content: [
          {
            type: 'text',
            text: `📝 笔记已添加 (ID: ${note.id})\n标题: ${note.title}\n内容: ${note.content}`
          }
        ]
      };

    case 'list_notes':
      const limit2 = args.limit || 10;
      const recentNotes = notes.slice(-limit2).reverse();

      if (recentNotes.length === 0) {
        return {
          content: [
            {
              type: 'text',
              text: '📝 暂无笔记'
            }
          ]
        };
      }

      const notesText = recentNotes
        .map(note => `📝 ID: ${note.id}\n标题: ${note.title}\n内容: ${note.content}\n时间: ${note.timestamp}`)
        .join('\n\n');

      return {
        content: [
          {
            type: 'text',
            text: `📝 最近的 ${recentNotes.length} 条笔记:\n\n${notesText}`
          }
        ]
      };

    default:
      throw new Error(`Unknown tool: ${name}`);
  }
}

// 创建 Express 应用
const app = express();
app.use(express.json());

// 存储会话状态
const sessions = new Map();

// MCP HTTP 端点
app.post('/mcp', async (req, res) => {
  try {
    const request = req.body;
    
    // 验证 JSON-RPC 格式
    if (!request || request.jsonrpc !== '2.0') {
      return res.status(400).json({
        jsonrpc: '2.0',
        id: request?.id || null,
        error: {
          code: -32600,
          message: 'Invalid Request'
        }
      });
    }

    // 处理初始化
    if (request.method === 'initialize') {
      const sessionId = req.headers['x-session-id'] || 'default';
      sessions.set(sessionId, { initialized: true });
      
      return res.json({
        jsonrpc: '2.0',
        id: request.id,
        result: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {},
            resources: {},
            prompts: {}
          },
          serverInfo: {
            name: 'simple-mcp-server',
            version: '1.0.0'
          }
        }
      });
    }

    // 处理工具列表
    if (request.method === 'tools/list') {
      return res.json({
        jsonrpc: '2.0',
        id: request.id,
        result: {
          tools: tools
        }
      });
    }

    // 处理工具调用
    if (request.method === 'tools/call') {
      const { name, arguments: args } = request.params;
      const callResponse = handleToolCall(name, args);
      return res.json({
        jsonrpc: '2.0',
        id: request.id,
        result: callResponse
      });
    }

    // 未知方法
    return res.status(404).json({
      jsonrpc: '2.0',
      id: request.id,
      error: {
        code: -32601,
        message: `Method not found: ${request.method}`
      }
    });

  } catch (error) {
    console.error('MCP endpoint error:', error);
    return res.status(500).json({
      jsonrpc: '2.0',
      id: req.body?.id || null,
      error: {
        code: -32603,
        message: 'Internal error'
      }
    });
  }
});

// GET 端点 - 返回服务器信息（用于健康检查）
app.get('/mcp', (req, res) => {
  res.json({
    name: 'simple-mcp-server',
    version: '1.0.0',
    protocol: 'MCP 2024-11-05',
    status: 'running',
    tools: tools.length,
    endpoint: 'POST /mcp for MCP requests'
  });
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`🚀 Simple MCP Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 MCP endpoint: http://localhost:${PORT}/mcp`);
});

#!/usr/bin/env python3
"""
Memory Knowledge Graph MCP 测试脚本
用于验证多端配置是否正常工作
"""

import requests
import json
import time

# 服务器配置
BASE_URL = "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    try:
        response = requests.get(f"{BASE_URL}/health", headers=HEADERS, timeout=10)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_add_memory():
    """测试添加记忆功能"""
    print("\n📝 测试添加记忆...")
    data = {
        "content": "这是一个测试记忆，用于验证MCP配置",
        "context": "MCP配置测试",
        "tags": ["测试", "MCP", "配置验证"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/tools/add_memory",
            headers=HEADERS,
            json=data,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 添加记忆成功")
            print(f"   记忆ID: {result.get('memory_id', 'N/A')}")
            return result.get('memory_id')
        else:
            print(f"❌ 添加记忆失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 添加记忆异常: {e}")
        return None

def test_search_memory():
    """测试搜索记忆功能"""
    print("\n🔍 测试搜索记忆...")
    data = {
        "query": "MCP配置",
        "limit": 5,
        "similarity_threshold": 0.5
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/tools/search_memory",
            headers=HEADERS,
            json=data,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 搜索记忆成功")
            memories = result.get('memories', [])
            print(f"   找到 {len(memories)} 条相关记忆")
            for i, memory in enumerate(memories[:3]):  # 只显示前3条
                print(f"   {i+1}. {memory.get('content', '')[:50]}...")
            return True
        else:
            print(f"❌ 搜索记忆失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 搜索记忆异常: {e}")
        return False

def test_get_related_memories(memory_id):
    """测试获取相关记忆功能"""
    if not memory_id:
        print("\n⚠️  跳过相关记忆测试 (没有有效的记忆ID)")
        return False
        
    print("\n🔗 测试获取相关记忆...")
    data = {
        "memory_id": memory_id,
        "depth": 2
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/tools/get_related_memories",
            headers=HEADERS,
            json=data,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取相关记忆成功")
            related = result.get('related_memories', [])
            print(f"   找到 {len(related)} 条相关记忆")
            return True
        else:
            print(f"❌ 获取相关记忆失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 获取相关记忆异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 Memory Knowledge Graph MCP")
    print("=" * 50)
    
    # 测试连接
    if not test_connection():
        print("\n❌ 服务器连接失败，无法继续测试")
        return
    
    # 测试添加记忆
    memory_id = test_add_memory()
    
    # 等待一下让服务器处理
    time.sleep(2)
    
    # 测试搜索记忆
    search_success = test_search_memory()
    
    # 测试获取相关记忆
    related_success = test_get_related_memories(memory_id)
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   连接测试: ✅")
    print(f"   添加记忆: {'✅' if memory_id else '❌'}")
    print(f"   搜索记忆: {'✅' if search_success else '❌'}")
    print(f"   相关记忆: {'✅' if related_success else '❌'}")
    
    if memory_id and search_success:
        print("\n🎉 Memory Knowledge Graph MCP 配置成功！")
        print("   现在可以在各个平台上使用这个MCP服务了。")
    else:
        print("\n⚠️  部分功能测试失败，请检查服务器状态和配置。")

if __name__ == "__main__":
    main()
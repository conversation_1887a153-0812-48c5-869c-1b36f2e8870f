# MCP HTTP Bridge 使用指南

## 🎯 概述

MCP HTTP Bridge 是一个将本地 MCP (Model Context Protocol) 服务器暴露为 HTTP API 的工具，通过内网穿透技术让 Coze、GenSpark 等外部服务能够访问您的本地 Notion MCP 服务器。

## 🚀 快速开始

### 1. 启动 MCP HTTP Bridge

```bash
# 进入项目目录
cd mcp-http-bridge

# 安装依赖（首次运行）
npm install

# 启动服务器
npm start
```

或者使用 Windows 批处理文件：
```bash
start.bat
```

### 2. 验证服务器运行

打开浏览器访问：http://localhost:3000

您应该看到管理界面，显示 MCP 服务器状态。

### 3. 设置内网穿透

#### 方式一：使用 Ngrok（推荐）

```bash
# 安装 ngrok
npm install -g ngrok

# 启动隧道
ngrok http 3000
```

#### 方式二：使用批处理脚本

```bash
setup-tunnel.bat
```

选择您喜欢的内网穿透工具，脚本会自动启动。

### 4. 配置外部服务

访问配置向导：http://localhost:3000/config-wizard.html

按照向导步骤配置您的外部服务。

## 📋 API 使用说明

### 基础端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/health` | GET | 检查服务器健康状态 |
| `/api/servers` | GET | 获取可用的 MCP 服务器列表 |
| `/api/servers/{server}/tools` | GET | 获取服务器工具列表 |
| `/api/call` | POST | 通用工具调用接口 |

### 通用调用接口

**端点**: `POST /api/call`

**请求格式**:
```json
{
  "server": "notion",
  "tool": "search_pages",
  "args": {
    "query": "搜索关键词"
  }
}
```

**响应格式**:
```json
{
  "success": true,
  "server": "notion",
  "tool": "search_pages",
  "result": {
    // 工具执行结果
  }
}
```

## 🤖 Coze 集成

### 配置步骤

1. 在 Coze 中创建新的 API 连接
2. 设置端点 URL：`https://your-ngrok-url.ngrok.io/api/call`
3. 方法：POST
4. 请求头：`Content-Type: application/json`

### 使用示例

```json
{
  "server": "notion",
  "tool": "create_page",
  "args": {
    "parent_id": "database_id",
    "title": "新页面标题",
    "content": "页面内容"
  }
}
```

### 常用工具

- `search_pages`: 搜索 Notion 页面
- `create_page`: 创建新页面
- `update_page`: 更新页面内容
- `get_page`: 获取页面信息

## ⚡ GenSpark 集成

### 配置步骤

1. 在 GenSpark 中配置 Base URL：`https://your-ngrok-url.ngrok.io`
2. 健康检查端点：`/health`
3. API 调用端点：`/api/call`

### 使用示例

```bash
curl -X POST https://your-ngrok-url.ngrok.io/api/call \
  -H "Content-Type: application/json" \
  -d '{
    "server": "memory",
    "tool": "add_memory",
    "args": {
      "content": "重要信息",
      "tags": ["工作", "项目"]
    }
  }'
```

## 🧠 Memory MCP 服务器

### 可用工具

- `add_memory`: 添加记忆
- `search_memory`: 搜索记忆
- `get_memory`: 获取特定记忆
- `update_memory`: 更新记忆
- `delete_memory`: 删除记忆

### 使用示例

```json
{
  "server": "memory",
  "tool": "search_memory",
  "args": {
    "query": "项目相关信息"
  }
}
```

## 🔧 高级配置

### 自定义 MCP 服务器

在 `server.js` 中修改 `MCP_SERVERS` 配置：

```javascript
const MCP_SERVERS = {
  your_server: {
    command: 'node',
    args: ['path/to/your/mcp/server.js'],
    env: {
      YOUR_API_KEY: 'your_api_key'
    }
  }
};
```

### Ngrok 高级配置

编辑 `ngrok.yml` 文件：

```yaml
version: "2"
authtoken: your_auth_token
tunnels:
  mcp-bridge:
    proto: http
    addr: 3000
    subdomain: your-custom-subdomain
    auth: "username:password"
```

### 环境变量

- `PORT`: HTTP 服务器端口（默认：3000）
- `NOTION_API_KEY`: Notion API 密钥
- `MEMORY_FILE_PATH`: 内存文件路径

## 🛠️ 故障排除

### 常见问题

1. **MCP 服务器启动失败**
   - 检查 Node.js 版本（需要 v16+）
   - 确认 API 密钥正确
   - 查看控制台错误信息

2. **内网穿透连接失败**
   - 确认防火墙设置
   - 检查网络连接
   - 尝试不同的内网穿透工具

3. **API 调用超时**
   - 检查服务器状态：`GET /health`
   - 确认工具名称正确
   - 增加请求超时时间

### 调试模式

启用详细日志：

```bash
DEBUG=* npm start
```

### 日志查看

- HTTP Bridge 日志：控制台输出
- MCP 服务器日志：stderr 输出
- 访问日志：浏览器开发者工具

## 🔒 安全建议

1. **保护 API 密钥**
   - 不要在代码中硬编码密钥
   - 使用环境变量存储敏感信息

2. **内网穿透安全**
   - 使用 HTTPS 隧道
   - 考虑添加基本认证
   - 限制访问 IP 范围

3. **生产环境**
   - 使用专业的内网穿透服务
   - 配置 SSL 证书
   - 实施访问控制

## 📞 获取帮助

1. 查看 Web 管理界面的状态信息
2. 使用配置向导生成配置
3. 检查 README.md 文件
4. 查看 configs/ 目录下的示例配置

## 🎉 成功案例

### Coze 集成示例

用户成功将 Notion MCP 服务器集成到 Coze 中，实现了：
- 自动搜索 Notion 页面
- 创建会议记录
- 更新项目状态

### GenSpark 集成示例

用户通过 GenSpark 访问内存 MCP 服务器，实现了：
- 知识库搜索
- 信息存储和检索
- 智能问答系统

## 📈 性能优化

1. **连接池管理**：复用 MCP 连接
2. **缓存策略**：缓存常用工具列表
3. **超时设置**：合理设置请求超时
4. **错误重试**：实现自动重试机制

---

**提示**: 如果您在使用过程中遇到问题，请先查看管理界面的状态信息，大多数问题都可以通过重启服务或检查配置来解决。

# 验证标准 MCP 配置文件
# 测试配置文件格式和服务器连接

Write-Host "🔍 验证标准 MCP 配置..." -ForegroundColor Cyan
Write-Host ""

# 检查配置文件
$configFiles = @(
    "..\simple-mcp-server-standard.json",
    "..\.mcp.json"
)

foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        Write-Host "📄 检查配置文件: $configFile" -ForegroundColor Green
        
        try {
            $config = Get-Content $configFile | ConvertFrom-Json
            
            if ($config.mcpServers) {
                Write-Host "✅ 配置文件格式正确" -ForegroundColor Green
                Write-Host "   包含服务器数量: $($config.mcpServers.PSObject.Properties.Count)" -ForegroundColor Gray
                
                foreach ($serverName in $config.mcpServers.PSObject.Properties.Name) {
                    $server = $config.mcpServers.$serverName
                    Write-Host "   - $serverName" -ForegroundColor Gray
                    Write-Host "     类型: $($server.type)" -ForegroundColor Gray
                    Write-Host "     URL: $($server.url)" -ForegroundColor Gray
                }
            } else {
                Write-Host "❌ 配置文件格式错误：缺少 mcpServers 字段" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ 配置文件 JSON 格式错误：$($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  配置文件不存在: $configFile" -ForegroundColor Yellow
    }
    Write-Host ""
}

# 测试服务器连接
Write-Host "🌐 测试服务器连接..." -ForegroundColor Cyan
Write-Host ""

$servers = @(
    @{
        Name = "Simple MCP Server"
        Url = "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp"
    },
    @{
        Name = "Memory Knowledge Graph"
        Url = "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp"
    }
)

$headers = @{'Content-Type' = 'application/json'}
$initBody = '{"jsonrpc": "2.0", "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "ConfigValidator", "version": "1.0.0"}}, "id": 1}'

foreach ($server in $servers) {
    Write-Host "🔗 测试 $($server.Name)..." -ForegroundColor Green
    
    try {
        $response = Invoke-WebRequest -Uri $server.Url -Method POST -Headers $headers -Body $initBody -TimeoutSec 10
        $result = $response.Content | ConvertFrom-Json
        
        if ($result.result.serverInfo) {
            Write-Host "✅ 连接成功" -ForegroundColor Green
            Write-Host "   服务器名称: $($result.result.serverInfo.name)" -ForegroundColor Gray
            Write-Host "   协议版本: $($result.result.protocolVersion)" -ForegroundColor Gray
            
            # 测试工具列表
            $toolsBody = '{"jsonrpc": "2.0", "method": "tools/list", "params": {}, "id": 2}'
            try {
                $toolsResponse = Invoke-WebRequest -Uri $server.Url -Method POST -Headers $headers -Body $toolsBody -TimeoutSec 10
                $toolsResult = $toolsResponse.Content | ConvertFrom-Json
                $toolCount = $toolsResult.result.tools.Count
                Write-Host "   可用工具: $toolCount 个" -ForegroundColor Gray
                
                if ($toolCount -gt 0) {
                    Write-Host "   工具列表:" -ForegroundColor Gray
                    foreach ($tool in $toolsResult.result.tools) {
                        Write-Host "     - $($tool.name): $($tool.description)" -ForegroundColor DarkGray
                    }
                }
            } catch {
                Write-Host "   ⚠️  工具列表获取失败" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ 连接失败：响应格式不正确" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 连接失败：$($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host ""
}

# 生成配置建议
Write-Host "💡 配置建议..." -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 推荐的标准配置文件内容：" -ForegroundColor Yellow
Write-Host ""
Write-Host @"
{
  "mcpServers": {
    "simple-mcp-server": {
      "type": "http",
      "url": "https://oh-proceedings-scene-surveys.trycloudflare.com/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
"@ -ForegroundColor Gray

Write-Host ""
Write-Host "📁 配置文件放置位置：" -ForegroundColor Yellow
Write-Host "   - 项目级：项目根目录的 .mcp.json" -ForegroundColor Gray
Write-Host "   - 用户级：用户配置目录" -ForegroundColor Gray
Write-Host ""

Write-Host "🔧 使用步骤：" -ForegroundColor Yellow
Write-Host "   1. 复制上述配置内容到 .mcp.json 文件" -ForegroundColor Gray
Write-Host "   2. 将文件放在项目根目录" -ForegroundColor Gray
Write-Host "   3. 重启通义灵码或相关 MCP 客户端" -ForegroundColor Gray
Write-Host "   4. 验证服务器是否出现在 MCP 列表中" -ForegroundColor Gray
Write-Host ""

Write-Host "🎉 配置验证完成！" -ForegroundColor Cyan

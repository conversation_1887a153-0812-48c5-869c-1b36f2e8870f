import { spawn } from 'child_process';

// 测试 stdio MCP 服务器
function testStdioServer() {
  console.log('🧪 测试 stdio MCP 服务器...\n');

  const server = spawn('node', ['stdio-server.js'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let responseBuffer = '';

  server.stdout.on('data', (data) => {
    responseBuffer += data.toString();
    
    // 处理完整的 JSON 响应
    const lines = responseBuffer.split('\n');
    responseBuffer = lines.pop() || '';
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const response = JSON.parse(line);
          console.log('📥 响应:', JSON.stringify(response, null, 2));
        } catch (e) {
          console.log('📄 输出:', line);
        }
      }
    }
  });

  server.stderr.on('data', (data) => {
    console.log('📝 日志:', data.toString().trim());
  });

  // 测试序列
  const tests = [
    {
      name: '初始化',
      message: {
        jsonrpc: '2.0',
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'Test', version: '1.0.0' }
        },
        id: 1
      }
    },
    {
      name: '获取工具列表',
      message: {
        jsonrpc: '2.0',
        method: 'tools/list',
        params: {},
        id: 2
      }
    },
    {
      name: '测试 echo 工具',
      message: {
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: 'echo',
          arguments: { text: 'Hello MCP!' }
        },
        id: 3
      }
    },
    {
      name: '测试 remember 工具',
      message: {
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: 'remember',
          arguments: { 
            content: '测试记忆：stdio 服务器工作正常',
            tags: ['测试', 'stdio', 'MCP']
          }
        },
        id: 4
      }
    }
  ];

  let testIndex = 0;

  function runNextTest() {
    if (testIndex < tests.length) {
      const test = tests[testIndex++];
      console.log(`\n🔬 运行测试: ${test.name}`);
      server.stdin.write(JSON.stringify(test.message) + '\n');
      
      // 等待一段时间后运行下一个测试
      setTimeout(runNextTest, 1000);
    } else {
      console.log('\n✅ 所有测试完成');
      server.stdin.end();
    }
  }

  // 开始测试
  setTimeout(runNextTest, 500);

  server.on('close', (code) => {
    console.log(`\n🔚 服务器退出，代码: ${code}`);
  });
}

testStdioServer();

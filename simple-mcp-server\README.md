# Simple MCP Server

🚀 一个简单、实用的 MCP (Model Context Protocol) 服务器，专为 GenSpark 等 AI 平台设计。

## ✨ 功能特性

- 🔌 **标准 MCP 协议**: 完全符合 MCP StreamableHttp 规范
- 🧠 **记忆系统**: 存储和搜索重要信息
- 📝 **笔记管理**: 快速添加和查看笔记
- 🔧 **实用工具**: 文本回显、数学计算等
- 🌐 **HTTP API**: 支持远程访问和集成
- ⚡ **轻量级**: 无复杂依赖，启动快速

## 🛠️ 可用工具

### 1. echo - 文本回显
回显输入的文本，用于测试连接。

**参数**:
- `text` (string): 要回显的文本

**示例**:
```json
{
  "name": "echo",
  "arguments": {
    "text": "Hello, MCP!"
  }
}
```

### 2. add - 数字加法
计算两个数字的和。

**参数**:
- `a` (number): 第一个数字
- `b` (number): 第二个数字

**示例**:
```json
{
  "name": "add",
  "arguments": {
    "a": 10,
    "b": 20
  }
}
```

### 3. remember - 存储记忆
保存重要信息到记忆系统。

**参数**:
- `content` (string): 要记住的内容
- `tags` (array, 可选): 分类标签

**示例**:
```json
{
  "name": "remember",
  "arguments": {
    "content": "今天学习了 MCP 协议的使用方法",
    "tags": ["学习", "MCP", "技术"]
  }
}
```

### 4. recall - 搜索记忆
根据关键词搜索已存储的记忆。

**参数**:
- `query` (string): 搜索关键词
- `limit` (number, 可选): 最大返回数量，默认 5

**示例**:
```json
{
  "name": "recall",
  "arguments": {
    "query": "MCP",
    "limit": 3
  }
}
```

### 5. add_note - 添加笔记
快速添加一条笔记。

**参数**:
- `title` (string): 笔记标题
- `content` (string): 笔记内容

**示例**:
```json
{
  "name": "add_note",
  "arguments": {
    "title": "会议要点",
    "content": "下周一开始新项目，需要准备技术方案"
  }
}
```

### 6. list_notes - 列出笔记
显示最近的笔记列表。

**参数**:
- `limit` (number, 可选): 最大返回数量，默认 10

**示例**:
```json
{
  "name": "list_notes",
  "arguments": {
    "limit": 5
  }
}
```

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动服务器
```bash
npm start
```

服务器将在端口 3001 上启动。

### 3. 配置 GenSpark

在 GenSpark 中添加 MCP 服务器：

**服务器名称**: Simple MCP Server
**服务器类型**: StreamableHttp
**服务器 URL**: `https://your-tunnel-url.com/mcp`
**请求头**: `{"Content-Type": "application/json"}`

## 🌐 API 端点

### MCP 端点
- **POST /mcp**: MCP 协议主端点
- **GET /mcp**: 返回 405 Method Not Allowed（符合规范）

### 健康检查
- **GET /health**: 服务器健康状态检查

## 📝 使用示例

### 在 GenSpark 中使用

1. **存储学习笔记**:
   - 工具: `remember`
   - 内容: "学会了如何配置 MCP 服务器"
   - 标签: ["学习", "技术"]

2. **搜索相关信息**:
   - 工具: `recall`
   - 查询: "MCP"

3. **快速计算**:
   - 工具: `add`
   - 参数: a=15, b=25

4. **添加待办事项**:
   - 工具: `add_note`
   - 标题: "今日任务"
   - 内容: "完成项目文档编写"

## 🔧 开发和扩展

### 添加新工具

1. 在 `tools` 数组中添加工具定义
2. 在 `handleToolCall` 函数中添加处理逻辑
3. 重启服务器

### 数据持久化

当前版本使用内存存储，重启后数据会丢失。如需持久化，可以：
- 添加文件存储
- 集成数据库
- 使用外部存储服务

## 🛡️ 安全注意事项

- 当前版本无身份验证，仅用于开发和测试
- 生产环境建议添加访问控制
- 使用 HTTPS 进行安全传输

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

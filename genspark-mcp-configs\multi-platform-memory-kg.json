{"name": "Memory Knowledge Graph - 多端配置", "description": "适用于安卓、Windows、Web端的Memory Knowledge Graph MCP配置", "platforms": {"android": {"type": "http", "config": {"url": "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "MCP-Android-Client/1.0"}, "timeout": 30000}}, "windows": {"type": "local", "config": {"command": "uvx", "args": ["mcp-server-http", "--url", "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp"], "env": {"FASTMCP_LOG_LEVEL": "INFO"}}}, "web": {"type": "websocket", "config": {"url": "wss://cuisine-miss-regardless-walls.trycloudflare.com/mcp/ws", "protocols": ["mcp"], "headers": {"Origin": "https://your-web-app.com"}}}}, "shared_tools": [{"name": "add_memory", "description": "添加记忆到知识图谱", "endpoint": "/tools/add_memory", "method": "POST"}, {"name": "search_memory", "description": "搜索知识图谱中的记忆", "endpoint": "/tools/search_memory", "method": "POST"}, {"name": "get_related_memories", "description": "获取相关记忆", "endpoint": "/tools/get_related_memories", "method": "POST"}, {"name": "update_memory", "description": "更新现有记忆", "endpoint": "/tools/update_memory", "method": "PUT"}, {"name": "delete_memory", "description": "删除记忆", "endpoint": "/tools/delete_memory", "method": "DELETE"}], "authentication": {"type": "none", "note": "当前服务器不需要认证，如需要可添加API密钥"}}
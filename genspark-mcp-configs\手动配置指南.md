# GenSpark MCP 手动配置指南

## 🎯 推荐方法：手动添加（最可靠）

由于配置文件格式可能存在兼容性问题，**强烈推荐使用手动配置方式**。

### 📋 Simple MCP Server 配置信息

请在 GenSpark 中手动填写以下信息：

```
服务器名称: Simple MCP Server
服务器类型: StreamableHttp
服务器 URL: https://oh-proceedings-scene-surveys.trycloudflare.com/mcp
描述: 轻量级 MCP 服务器，提供记忆、笔记和基础工具功能
请求头: {"Content-Type": "application/json"}
```

### 🔧 详细配置步骤

1. **打开 GenSpark**
2. **进入 MCP 设置**
   - 找到 "MCP 服务器" 或 "MCP 配置" 选项
   - 点击 "添加新服务器" 或 "+" 按钮

3. **填写服务器信息**
   ```
   字段名称              填写内容
   ─────────────────────────────────────────
   服务器名称            Simple MCP Server
   服务器类型/协议        StreamableHttp
   服务器地址/URL        https://oh-proceedings-scene-surveys.trycloudflare.com/mcp
   描述                 轻量级 MCP 服务器，提供记忆、笔记和基础工具功能
   请求头/Headers       {"Content-Type": "application/json"}
   ```

4. **保存配置**
   - 点击 "保存" 或 "确认" 按钮
   - 等待连接测试

5. **验证连接**
   - 查看服务器状态是否为 "已连接" 或 "在线"
   - 尝试使用 echo 工具测试

### ✅ 测试连接

配置完成后，在 GenSpark 中说：

```
"请回显：Hello MCP!"
```

如果看到回复 "Echo: Hello MCP!"，说明配置成功！

### 🛠️ 可用工具

配置成功后，您可以使用以下工具：

| 工具名 | 功能 | 示例用法 |
|--------|------|----------|
| `echo` | 文本回显 | "请回显：测试文本" |
| `add` | 数字加法 | "计算 15 + 25" |
| `remember` | 存储记忆 | "请记住：今天学会了配置 MCP" |
| `recall` | 搜索记忆 | "帮我找找关于 MCP 的记忆" |
| `add_note` | 添加笔记 | "添加笔记：明天的待办事项" |
| `list_notes` | 列出笔记 | "显示我的笔记列表" |

### 🔄 如果配置失败

如果手动配置也失败，请尝试以下方法：

1. **检查 URL 格式**
   - 确保 URL 完整且正确
   - 检查是否有多余的空格

2. **尝试不同的服务器类型**
   - 如果 "StreamableHttp" 不可用，尝试：
     - "HTTP"
     - "Http"
     - "streamableHttp"
     - "STREAMABLE_HTTP"

3. **简化请求头**
   - 如果复杂的请求头不被支持，尝试：
     - `{"Content-Type": "application/json"}`
     - `Content-Type: application/json`
     - 或者留空

4. **检查服务器状态**
   - 确认我们的服务器正在运行
   - 可以通过浏览器访问健康检查端点

### 🌐 服务器状态检查

您可以通过以下方式检查服务器是否正常运行：

**健康检查 URL**: 
```
https://oh-proceedings-scene-surveys.trycloudflare.com/health
```

在浏览器中访问此 URL，应该看到类似以下的响应：
```json
{
  "status": "healthy",
  "server": "simple-mcp-server",
  "timestamp": "2025-07-16T..."
}
```

### 📞 获取帮助

如果仍然无法配置成功：

1. **检查错误信息**
   - 记录具体的错误提示
   - 截图保存错误界面

2. **尝试其他配置文件**
   - 我们提供了多种格式的配置文件
   - 可以尝试不同的版本

3. **联系技术支持**
   - 提供错误信息和配置截图
   - 说明尝试过的配置方法

## 🎉 配置成功后

一旦配置成功，您就可以：

1. **测试基本功能**
   - "请回显：Hello World"
   - "计算 10 + 20"

2. **开始使用记忆功能**
   - "请记住：我今天学会了配置 MCP 服务器"
   - "帮我找找关于学习的记忆"

3. **管理笔记**
   - "添加笔记：明天要完成的任务"
   - "显示我的笔记列表"

4. **探索高级功能**
   - 组合使用多个工具
   - 建立个人知识库

祝您配置成功，享受 MCP 的强大功能！🚀

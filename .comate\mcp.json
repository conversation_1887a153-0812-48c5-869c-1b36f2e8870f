{"mcpServers": {"notion": {"command": "npx", "args": ["@ramidecodes/mcp-server-notion@latest"], "env": {"NOTION_API_KEY": "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"}}, "memory-knowledge-graph": {"command": "uvx", "args": ["mcp-server-http", "--url", "https://cuisine-miss-regardless-walls.trycloudflare.com/mcp"], "env": {"FASTMCP_LOG_LEVEL": "INFO"}, "disabled": false, "autoApprove": ["search_memory", "get_related_memories"]}}}
# 配置文件验证脚本
# 验证所有 JSON 配置文件的格式是否正确

Write-Host "🔍 开始验证配置文件..." -ForegroundColor Cyan
Write-Host ""

$configFiles = @(
    "simple-mcp-server-standard.json",
    "simple-mcp-server-basic.json", 
    "simple-mcp-server-genspark.json",
    "simple-mcp-server-array.json",
    "memory-knowledge-graph.json"
)

$configPath = ".."

foreach ($file in $configFiles) {
    $fullPath = Join-Path $configPath $file
    
    Write-Host "📄 验证文件: $file" -ForegroundColor Green
    
    if (Test-Path $fullPath) {
        try {
            $content = Get-Content $fullPath -Raw
            $json = $content | ConvertFrom-Json
            Write-Host "   ✅ JSON 格式正确" -ForegroundColor Green
            
            # 检查基本字段
            if ($file -like "*standard*") {
                if ($json.mcpServers) {
                    Write-Host "   ✅ 包含 mcpServers 字段" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ 缺少 mcpServers 字段" -ForegroundColor Red
                }
            }
            elseif ($file -like "*basic*") {
                if ($json.name -and $json.url) {
                    Write-Host "   ✅ 包含基本字段 (name, url)" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ 缺少基本字段" -ForegroundColor Red
                }
            }
            elseif ($file -like "*genspark*") {
                if ($json.servers) {
                    Write-Host "   ✅ 包含 servers 数组" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ 缺少 servers 数组" -ForegroundColor Red
                }
            }
            elseif ($file -like "*array*") {
                if ($json -is [array]) {
                    Write-Host "   ✅ 是数组格式" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ 不是数组格式" -ForegroundColor Red
                }
            }
            
        } catch {
            Write-Host "   ❌ JSON 格式错误: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "   ❌ 文件不存在" -ForegroundColor Red
    }
    
    Write-Host ""
}

Write-Host "🎯 配置文件使用建议:" -ForegroundColor Yellow
Write-Host "1. 优先尝试 simple-mcp-server-standard.json" -ForegroundColor Gray
Write-Host "2. 如果不支持，尝试 simple-mcp-server-basic.json" -ForegroundColor Gray
Write-Host "3. 针对 GenSpark 可尝试 simple-mcp-server-genspark.json" -ForegroundColor Gray
Write-Host "4. 批量导入可使用 simple-mcp-server-array.json" -ForegroundColor Gray
Write-Host ""

Write-Host "📋 手动配置信息:" -ForegroundColor Yellow
Write-Host "服务器名称: Simple MCP Server" -ForegroundColor Gray
Write-Host "服务器类型: StreamableHttp" -ForegroundColor Gray
Write-Host "服务器 URL: https://oh-proceedings-scene-surveys.trycloudflare.com/mcp" -ForegroundColor Gray
Write-Host "请求头: {`"Content-Type`": `"application/json`"}" -ForegroundColor Gray
Write-Host ""

Write-Host "✅ 配置文件验证完成！" -ForegroundColor Cyan

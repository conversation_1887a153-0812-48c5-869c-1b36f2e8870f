#!/usr/bin/env node

/**
 * 标准 MCP stdio 服务器
 * 专为通义灵码等 MCP 客户端设计
 */

// 简单的内存存储
const memories = [];
const notes = [];

// 工具定义
const tools = [
  {
    name: 'echo',
    description: 'Echo back the input text',
    inputSchema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: 'Text to echo back'
        }
      },
      required: ['text']
    }
  },
  {
    name: 'add',
    description: 'Add two numbers',
    inputSchema: {
      type: 'object',
      properties: {
        a: {
          type: 'number',
          description: 'First number'
        },
        b: {
          type: 'number',
          description: 'Second number'
        }
      },
      required: ['a', 'b']
    }
  },
  {
    name: 'remember',
    description: 'Store a memory or important information',
    inputSchema: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: 'The content to remember'
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: 'Optional tags for categorization'
        }
      },
      required: ['content']
    }
  },
  {
    name: 'recall',
    description: 'Search and retrieve stored memories',
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search query to find relevant memories'
        },
        limit: {
          type: 'number',
          description: 'Maximum number of results to return',
          default: 5
        }
      },
      required: ['query']
    }
  },
  {
    name: 'add_note',
    description: 'Add a quick note',
    inputSchema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Note title'
        },
        content: {
          type: 'string',
          description: 'Note content'
        }
      },
      required: ['title', 'content']
    }
  },
  {
    name: 'list_notes',
    description: 'List all stored notes',
    inputSchema: {
      type: 'object',
      properties: {
        limit: {
          type: 'number',
          description: 'Maximum number of notes to return',
          default: 10
        }
      }
    }
  }
];

// 工具处理函数
function handleToolCall(name, args) {
  switch (name) {
    case 'echo':
      return {
        content: [
          {
            type: 'text',
            text: `Echo: ${args.text}`
          }
        ]
      };

    case 'add':
      const result = args.a + args.b;
      return {
        content: [
          {
            type: 'text',
            text: `${args.a} + ${args.b} = ${result}`
          }
        ]
      };

    case 'remember':
      const memory = {
        id: Date.now(),
        content: args.content,
        tags: args.tags || [],
        timestamp: new Date().toISOString()
      };
      memories.push(memory);
      return {
        content: [
          {
            type: 'text',
            text: `✅ 记忆已保存 (ID: ${memory.id})\n内容: ${memory.content}\n标签: ${memory.tags.join(', ') || '无'}`
          }
        ]
      };

    case 'recall':
      const query = args.query.toLowerCase();
      const limit = args.limit || 5;
      const matchingMemories = memories
        .filter(memory => 
          memory.content.toLowerCase().includes(query) ||
          memory.tags.some(tag => tag.toLowerCase().includes(query))
        )
        .slice(0, limit);

      if (matchingMemories.length === 0) {
        return {
          content: [
            {
              type: 'text',
              text: `❌ 没有找到与 "${args.query}" 相关的记忆`
            }
          ]
        };
      }

      const resultText = matchingMemories
        .map(memory => `📝 ID: ${memory.id}\n内容: ${memory.content}\n标签: ${memory.tags.join(', ') || '无'}\n时间: ${memory.timestamp}`)
        .join('\n\n');

      return {
        content: [
          {
            type: 'text',
            text: `🔍 找到 ${matchingMemories.length} 条相关记忆:\n\n${resultText}`
          }
        ]
      };

    case 'add_note':
      const note = {
        id: Date.now(),
        title: args.title,
        content: args.content,
        timestamp: new Date().toISOString()
      };
      notes.push(note);
      return {
        content: [
          {
            type: 'text',
            text: `📝 笔记已添加 (ID: ${note.id})\n标题: ${note.title}\n内容: ${note.content}`
          }
        ]
      };

    case 'list_notes':
      const limit2 = args.limit || 10;
      const recentNotes = notes.slice(-limit2).reverse();

      if (recentNotes.length === 0) {
        return {
          content: [
            {
              type: 'text',
              text: '📝 暂无笔记'
            }
          ]
        };
      }

      const notesText = recentNotes
        .map(note => `📝 ID: ${note.id}\n标题: ${note.title}\n内容: ${note.content}\n时间: ${note.timestamp}`)
        .join('\n\n');

      return {
        content: [
          {
            type: 'text',
            text: `📝 最近的 ${recentNotes.length} 条笔记:\n\n${notesText}`
          }
        ]
      };

    default:
      throw new Error(`Unknown tool: ${name}`);
  }
}

// MCP 消息处理
function handleMCPMessage(message) {
  const { method, params, id } = message;

  switch (method) {
    case 'initialize':
      return {
        jsonrpc: '2.0',
        id,
        result: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {}
          },
          serverInfo: {
            name: 'simple-mcp-server',
            version: '1.0.0'
          }
        }
      };

    case 'tools/list':
      return {
        jsonrpc: '2.0',
        id,
        result: {
          tools
        }
      };

    case 'tools/call':
      try {
        const result = handleToolCall(params.name, params.arguments);
        return {
          jsonrpc: '2.0',
          id,
          result
        };
      } catch (error) {
        return {
          jsonrpc: '2.0',
          id,
          error: {
            code: -32000,
            message: error.message
          }
        };
      }

    default:
      return {
        jsonrpc: '2.0',
        id,
        error: {
          code: -32601,
          message: `Method not found: ${method}`
        }
      };
  }
}

// Stdio 模式
console.error('🚀 Simple MCP Server starting in stdio mode...');

let buffer = '';

process.stdin.setEncoding('utf8');

process.stdin.on('data', (chunk) => {
  buffer += chunk;
  
  // 处理完整的 JSON 消息
  const lines = buffer.split('\n');
  buffer = lines.pop() || ''; // 保留不完整的行
  
  for (const line of lines) {
    if (line.trim()) {
      try {
        const message = JSON.parse(line);
        const response = handleMCPMessage(message);
        console.log(JSON.stringify(response));
      } catch (error) {
        console.error('Error processing message:', error.message);
      }
    }
  }
});

process.stdin.on('end', () => {
  console.error('🔚 MCP Server shutting down...');
  process.exit(0);
});

// 处理进程信号
process.on('SIGINT', () => {
  console.error('🔚 MCP Server interrupted...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.error('🔚 MCP Server terminated...');
  process.exit(0);
});

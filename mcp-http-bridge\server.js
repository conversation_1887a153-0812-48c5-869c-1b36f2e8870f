import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import { spawn } from 'child_process';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// 中间件配置
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// MCP 服务器配置
const MCP_SERVERS = {
  notion: {
    command: 'node',
    args: ['-e', `
      const { spawn } = require('child_process');
      const proc = spawn('npm', ['exec', '@ramidecodes/mcp-server-notion@latest'], {
        stdio: 'inherit',
        env: { ...process.env, NOTION_API_KEY: '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' }
      });
      proc.on('close', (code) => process.exit(code));
    `],
    env: {
      NOTION_API_KEY: '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'
    }
  },
  memory: {
    command: 'node',
    args: ['../memory-mcp/memory-server/dist/index.js'],
    env: {}
  }
};

// 活跃的 MCP 连接
const activeConnections = new Map();

// MCP 服务器管理类
class MCPServerManager {
  constructor(serverName, config) {
    this.serverName = serverName;
    this.config = config;
    this.process = null;
    this.isReady = false;
    this.messageQueue = [];
    this.responseHandlers = new Map();
    this.requestId = 0;
  }

  async start() {
    return new Promise((resolve, reject) => {
      console.log(`启动 MCP 服务器: ${this.serverName}`);
      
      this.process = spawn(this.config.command, this.config.args, {
        env: { ...process.env, ...this.config.env },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.process.stdout.on('data', (data) => {
        this.handleMessage(data.toString());
      });

      this.process.stderr.on('data', (data) => {
        console.error(`${this.serverName} stderr:`, data.toString());
      });

      this.process.on('close', (code) => {
        console.log(`${this.serverName} 进程退出，代码: ${code}`);
        this.isReady = false;
      });

      this.process.on('error', (error) => {
        console.error(`${this.serverName} 启动失败:`, error);
        reject(error);
      });

      // 发送初始化消息
      setTimeout(() => {
        this.sendMessage({
          jsonrpc: '2.0',
          id: this.getNextId(),
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {
              tools: {}
            },
            clientInfo: {
              name: 'mcp-http-bridge',
              version: '1.0.0'
            }
          }
        });
        
        setTimeout(() => {
          this.isReady = true;
          resolve();
        }, 2000);
      }, 1000);
    });
  }

  getNextId() {
    return ++this.requestId;
  }

  handleMessage(data) {
    const lines = data.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      try {
        const message = JSON.parse(line);
        
        if (message.id && this.responseHandlers.has(message.id)) {
          const handler = this.responseHandlers.get(message.id);
          this.responseHandlers.delete(message.id);
          handler(message);
        }
      } catch (error) {
        console.error(`解析消息失败 (${this.serverName}):`, error, 'Raw data:', line);
      }
    }
  }

  sendMessage(message) {
    if (this.process && this.process.stdin.writable) {
      const jsonMessage = JSON.stringify(message) + '\n';
      this.process.stdin.write(jsonMessage);
    }
  }

  async callTool(toolName, args = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isReady) {
        reject(new Error(`MCP 服务器 ${this.serverName} 未就绪`));
        return;
      }

      const id = this.getNextId();
      const message = {
        jsonrpc: '2.0',
        id,
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: args
        }
      };

      this.responseHandlers.set(id, (response) => {
        if (response.error) {
          reject(new Error(response.error.message || '工具调用失败'));
        } else {
          resolve(response.result);
        }
      });

      this.sendMessage(message);

      // 超时处理
      setTimeout(() => {
        if (this.responseHandlers.has(id)) {
          this.responseHandlers.delete(id);
          reject(new Error('请求超时'));
        }
      }, 30000);
    });
  }

  async listTools() {
    return new Promise((resolve, reject) => {
      if (!this.isReady) {
        reject(new Error(`MCP 服务器 ${this.serverName} 未就绪`));
        return;
      }

      const id = this.getNextId();
      const message = {
        jsonrpc: '2.0',
        id,
        method: 'tools/list'
      };

      this.responseHandlers.set(id, (response) => {
        if (response.error) {
          reject(new Error(response.error.message || '获取工具列表失败'));
        } else {
          resolve(response.result);
        }
      });

      this.sendMessage(message);

      setTimeout(() => {
        if (this.responseHandlers.has(id)) {
          this.responseHandlers.delete(id);
          reject(new Error('请求超时'));
        }
      }, 10000);
    });
  }

  stop() {
    if (this.process) {
      this.process.kill();
      this.process = null;
      this.isReady = false;
    }
  }
}

// 初始化 MCP 服务器
async function initializeMCPServers() {
  for (const [name, config] of Object.entries(MCP_SERVERS)) {
    try {
      const manager = new MCPServerManager(name, config);
      await manager.start();
      activeConnections.set(name, manager);
      console.log(`✅ ${name} MCP 服务器启动成功`);
    } catch (error) {
      console.error(`❌ ${name} MCP 服务器启动失败:`, error.message);
    }
  }
}

// API 路由

// 健康检查
app.get('/health', (req, res) => {
  const status = {};
  for (const [name, manager] of activeConnections) {
    status[name] = {
      ready: manager.isReady,
      pid: manager.process?.pid || null
    };
  }

  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    servers: status
  });
});

// 获取所有可用的 MCP 服务器
app.get('/api/servers', (req, res) => {
  const servers = Array.from(activeConnections.keys());
  res.json({ servers });
});

// 获取指定服务器的工具列表
app.get('/api/servers/:serverName/tools', async (req, res) => {
  const { serverName } = req.params;
  const manager = activeConnections.get(serverName);

  if (!manager) {
    return res.status(404).json({ error: `服务器 ${serverName} 未找到` });
  }

  try {
    const tools = await manager.listTools();
    res.json(tools);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 调用指定服务器的工具
app.post('/api/servers/:serverName/tools/:toolName', async (req, res) => {
  const { serverName, toolName } = req.params;
  const args = req.body || {};

  const manager = activeConnections.get(serverName);

  if (!manager) {
    return res.status(404).json({ error: `服务器 ${serverName} 未找到` });
  }

  try {
    const result = await manager.callTool(toolName, args);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 通用工具调用接口（用于外部服务）
app.post('/api/call', async (req, res) => {
  const { server, tool, args = {} } = req.body;

  if (!server || !tool) {
    return res.status(400).json({
      error: '缺少必需参数: server 和 tool'
    });
  }

  const manager = activeConnections.get(server);

  if (!manager) {
    return res.status(404).json({
      error: `服务器 ${server} 未找到`,
      availableServers: Array.from(activeConnections.keys())
    });
  }

  try {
    const result = await manager.callTool(tool, args);
    res.json({
      success: true,
      server,
      tool,
      result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      server,
      tool,
      error: error.message
    });
  }
});

// WebSocket 支持（用于实时通信）
wss.on('connection', (ws) => {
  console.log('新的 WebSocket 连接');

  ws.on('message', async (message) => {
    try {
      const data = JSON.parse(message);
      const { server, tool, args = {} } = data;

      if (!server || !tool) {
        ws.send(JSON.stringify({
          error: '缺少必需参数: server 和 tool'
        }));
        return;
      }

      const manager = activeConnections.get(server);

      if (!manager) {
        ws.send(JSON.stringify({
          error: `服务器 ${server} 未找到`
        }));
        return;
      }

      const result = await manager.callTool(tool, args);
      ws.send(JSON.stringify({
        success: true,
        server,
        tool,
        result
      }));
    } catch (error) {
      ws.send(JSON.stringify({
        success: false,
        error: error.message
      }));
    }
  });

  ws.on('close', () => {
    console.log('WebSocket 连接关闭');
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    console.log('🚀 启动 MCP HTTP Bridge...');

    // 初始化 MCP 服务器
    await initializeMCPServers();

    // 启动 HTTP 服务器
    server.listen(PORT, () => {
      console.log(`🌐 HTTP Bridge 服务器运行在 http://localhost:${PORT}`);
      console.log(`📊 健康检查: http://localhost:${PORT}/health`);
      console.log(`🔧 管理界面: http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');

  for (const [name, manager] of activeConnections) {
    console.log(`关闭 ${name} MCP 服务器`);
    manager.stop();
  }

  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

startServer();
